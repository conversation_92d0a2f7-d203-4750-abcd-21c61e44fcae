<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit('create')" destroyOnClose  :maskClosable="false"
    @cancel="handleCancel">
    <h1 style="text-align: center;font-size: 18px;font-weight: 600;">采购订单单号：{{ orderInfo.orderNo }}</h1>
    <div class="dpflex jcc aic">
      <div>采购订单创建时间:{{ orderInfo.makeTime }}</div>
      <div style="margin-left: 100px;">采购订单创建人:{{ orderInfo.purchaseUser }}</div>
    </div>
    <a-form style="height: 80px" ref="formRef" :model="formState" :rules="rules" layout="inline"
      :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
      <a-form-item label="结算类型" name="settlementType">
        <JDictSelectTag type="select" :disabled="isEdit" v-model:value="formState.settlementType"
          @change="changeSettle" dictCode="settlementbills_type" allowClear placeholder="请选择结算类型"
          style="width: 220px" />
      </a-form-item>
      <a-form-item label="供应商" name="supplierId">
        <!--  -->
        <a-select :disabled="isEdit" @change="changeSupplier" @click="handleSupplierClick"
          v-model:value="formState.supplierId" placeholder="请选择供应商" style="width: 220px" allowClear showSearch
          optionFilterProp="supplierNameSupplyPycode" :options="lists.supplieList">
        </a-select>
      </a-form-item>
      <a-form-item label="响应仓库" name="purchaseStorageId">
        <a-select :disabled="isEdit" @change="changeStore" @click="handleClickStore"
          v-model:value="formState.purchaseStorageId" placeholder="请选择响应仓库" style="width: 220px" allowClear showSearch
          optionFilterProp="storageNameAbbr" :options="lists.storeList">
        </a-select>
      </a-form-item>
    </a-form>
    <div class="btns">
      <!-- <a-button class="ml10" type="primary" @click="handleAddGoods">添加物资</a-button> -->
      <a-button class="ml10" type="primary" @click="handleHistory">历史单导入</a-button>
      <a-button class="ml10" type="primary" @click="handleFundsInfo">资金来源</a-button>
      <a-button class="ml10" type="primary" @click="handleRemarkDepart('more',null)">批量备注科室</a-button>
      <a-button class="ml10" type="primary" @click="handleSum">单据汇总</a-button>
      <a-button class="ml10" type="primary" v-auth="'purchOrderManage:importExcel'" @click="handleExcel">Excel导入</a-button>
      <!-- <a-button class="ml10" type="primary" @click="handleAddDepart">备注科室</a-button> -->
    </div>
    <JVxeTable id="saveTable" ref="tableRef" stripe rowNumber rowSelection resizable asyncRemove :toolbarConfig="false"
      :maxHeight="480" :checkboxConfig="{ range: true }" :disabledRows="{ input: ['text--16', 'text--18'] }"
      :loading="false" :disabled="false" :columns="orderColumns" :dataSource="innerData" :toolbar="false" @cell-click="handleCell">
      <template #goodsCode="prpos">
        <a-input v-if="prpos.row.goodsId === '-1'" v-model:value="prpos.row.goodsCode"
          @focus="handaleChangeInp(prpos, prpos.row)" @change="handaleChangeInp(prpos, prpos.row)" />
        <span v-else>{{ prpos.row.goodsCode }}</span>
      </template>
      <template #goodsName="prpos">
        <a-input v-if="prpos.row.goodsId === '-1'" v-model:value="prpos.row.goodsName"
          @focus="handaleChangeInp(prpos, prpos.rowNumber)" @change="handaleChangeInp(prpos, prpos.row)" />
        <span v-else>{{ prpos.row.goodsName }}</span>
      </template>
      <template #goodsSpecs="prpos">
        <a-input v-if="prpos.row.goodsId === '-1'" v-model:value="prpos.row.goodsSpecs"
          @focus="handaleChangeInp(prpos, prpos.row)" @change="handaleChangeInp(prpos, prpos.row)" />
        <span v-else>{{ prpos.row.goodsSpecs }}</span>
      </template>

      <template #purchaseNum="prpos">
        <a-input v-if="prpos.row.goodsId !== '-1' && prpos.row.quantitativePackageFlag == 1"
          @blur="handleBlur(prpos.row, $event)" @input="handleInp($event, prpos.row)"
          v-model:value="prpos.row.purchaseNum" placeholder="请输入数量" />
        <a-input v-if="prpos.row.goodsId !== '-1' && prpos.row.quantitativePackageFlag != 1"
          @input="handleInp($event, prpos.row)" v-model:value="prpos.row.purchaseNum" placeholder="请输入数量" />
      </template>
      <template #departRemarkStr="prpos">
        <a-input disabled v-model:value="prpos.row.departRemarkStr" v-if="prpos.row.goodsId != '-1'">
          <template #suffix>
            <a-popover v-model:visible="prpos.row.visible" trigger="click" placement="right">
              <template #content>
                <a-textarea disabled v-model:value="prpos.row.departRemarkStr" placeholder="" :rows="8" allow-clear/>
              </template>
              <fullscreen-outlined />
            </a-popover>
          </template>
        </a-input>
      </template>
      <!-- 定数包规格 -->
      <template #quantitativePackageList="prpos">
        <a-select v-if="prpos.row.quantitativePackageFlag == 1" @change="handleSelect(prpos.row, $event)"
          style="width: 200;" v-model:value="prpos.row.quantitativePackageId" :options="prpos.row.packageList">
        </a-select> 
      </template>
      <!-- 定数包数量 -->
      <template #packageNum="prpos">
        <span v-if="prpos.row.quantitativePackageFlag == 1">{{ prpos.row.packageNum }}</span>
      </template>
      <template #goodsSpecsDetail="prpos">
        <!-- options 展示列表  readOnly 不可编辑 -->
        <a-select v-if="prpos.row.goodsSpecsDetailOperationType === 'options'" style="width: 220px;"
          v-model:value="prpos.row.goodsSpecsDetail" :options="prpos.row.goodsSpecsDetailOptions">
        </a-select>
        <span v-else>{{ prpos.row.goodsSpecsDetail }}</span>
      </template>

      <template #action="props">
        <a-button v-if="props.row.goodsId != '-1'" type="link" @click="handleRemarkDepart('one',props)">备注科室</a-button>
        <a-button v-if="props.row.goodsId != '-1'" type="link" @click="handleDel(props)">
          <template #icon><delete-outlined /></template>删除
        </a-button>
      </template>
    </JVxeTable>
    <div class="check-table" :style="getstyle" ref="dragElement" @mousedown="mousedownFunction"
      :class="searchFlg ? 'boxahadow' : 'boxnoshow'">
      <BasicTable v-show="searchFlg" bordered :show-action-column="false" size="middle" rowKey="goodsId"
        :canResize="false" :columns="searchColumns" :dataSource="searchData" :pagination="false" :ellipsis="true"
        :scroll="{ y: 300 }" @row-click="handleSearchRowClick">
        <template #action="{ record }">
          <TableAction :actions="[
            {
              label: '添加',
              icon: 'ic:outline-delete-outline',
              onClick: handleAdd.bind(null, record),
            },
          ]" />
        </template>
        <template #footer>
          <div class="page" style="text-align: right">
            <Pagination @change="changePage" @showSizeChange="changeSize" :defaultPageSize="10"
              v-model:current="page.pageNo" v-model:pageSize="page.pageSize" size="small" :total="total"
              :pageSizeOptions="['10', '50', '100', '500']" :show-total="(total) => `共 ${total} 条数据`" show-size-changer
              show-quick-jumper />
          </div>
        </template>
      </BasicTable>
    </div>
    <HistoryOrder @register="HistoryOrders" @result="result"></HistoryOrder>
    <FundsInfo  @getFoundsInfo="getFoundsInfo" @register="FundsInfos"></FundsInfo>
    <AddDepartRemarkModal @send-res="sendRes" @register="AddDepartRemarkModals"></AddDepartRemarkModal>
    <!-- <SelectDepartMaterial @register="regModal" @returndata="setMaterialValue"></SelectDepartMaterial> -->
    <ExportModal @register="exportModals" @upload-Res="uploadRes"></ExportModal>
    <template #appendFooter>
      <a-button v-auth="'purchOrderManage:savePushOrder'" v-if="!isEdit" type="primary" @click="handleSubmit('push')">生成采购订单并推送</a-button>
    </template>
    <!-- <AddDepaetModal @send-result="sendResult"  @register="AddDepaetModals"></AddDepaetModal> -->
  </BasicModal>
</template>

<script lang="ts" setup>
import {
  ref,
  watch,
  createVNode,
  unref,
  reactive,
  onMounted,
  nextTick,
  computed
} from "vue";
import { ValidateErrorEntity } from "ant-design-vue/es/form/interface";
import { BasicTable, TableAction } from "/@/components/Table";
import { BasicModal, useModalInner } from "/@/components/Modal";
import {
  suppliernoPagelist,
  getspdStoragelist,
  VoByGoodsCommon,
  getSearchUserList
} from "/@/api/common/api";
import AddDepartRemarkModal from './AddDepartRemark.vue';
import FundsInfo from "./FundsInfo.vue";
import { addOrder, orderEdit, detailList, collect, pushOrder} from "../index.api";
import { purchasePreview } from "../../purchPlanManage/index.api";
import JDictSelectTag from "/@/components/Form/src/jeecg/components/JDictSelectTag.vue";
import { useDebounceFn } from "@vueuse/core";
import HistoryOrder from "./HistoryOrder.vue";
import ExportModal from './exportModal.vue';
const [FundsInfos, { openModal: fundsInfos }] = useModal();
import { useModal } from "/@/components/Modal";
import { orderColumns, searchColumns } from "../index.data";
import { Modal } from "ant-design-vue";
import { ExclamationCircleOutlined, FullscreenOutlined } from "@ant-design/icons-vue";
import { useMessage } from "/@/hooks/web/useMessage";
import { Pagination } from "ant-design-vue";
import { setInpLimit } from '/@/utils';
import { JVxeTableInstance } from '/@/components/jeecg/JVxeTable/types';
import { useUserStore } from "/@/store/modules/user";
const userStore: any = useUserStore();
import { useDraggable } from '/@/spdHooks/useDraggable'
const { dragElement, mousedownFunction } = useDraggable();
const [AddDepartRemarkModals, { openModal: openAddDepartRemarkModals }] = useModal();
const [exportModals, { openModal: openExportModal }] = useModal();
const tableRef = ref<JVxeTableInstance>(); // vxe表格实例

const emit = defineEmits(["success"]);
const { createMessage, createConfirm } = useMessage();
const [HistoryOrders, { openModal: historyOrder }] = useModal();
// const [regModal, { openModal }] = useModal();
const innerData = ref<any[]>([]);
const searchData = ref<any[]>([]); //子表格data
const searchFlg = ref(false); //子表格显隐
const formRef = ref();
const formState = reactive<any>({
  supplierId: undefined,
  id: undefined,
  settlementType: "2",
  purchaseStorageId: undefined,
});
const userList = ref<any>([]) //用户列表
let isEdit = ref();
// 分页搜索传参
const page = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  goodsName__goodsSimpleCode__goodsCommonName__goodsCode__goodsSpecs_orFlag: undefined,
});
const orderInfo = ref<any>({}); // 订单信息
const pageY = ref(0)
const getstyle = computed(() => {
  if (pageY.value > 600) {
    return { bottom: document.documentElement.clientHeight - pageY.value + "px", top: 'unset' };
  } else {
    return { top: -30 + pageY.value + "px" };
  }
});
const total = ref(0);
const getListm = () => {
  (page.settlementType = formState.settlementType),
    (page.supplierId = formState.supplierId),
    (page.storageId = formState.purchaseStorageId),
    (page.applyFlag_MultiString = '0,1'),//是否可请领, 
    (page.tempPurchaseFlag_MultiString = '0,1'),// 是否临采
    VoByGoodsCommon(page).then((res) => {
      total.value = res.total;
      searchData.value = res.records;
    });
};
const changePage = (pag, pageSize) => {
  page.pageNo = pag;
  getListm();
};
const changeSize = (current, size) => {
  page.pageNo = 1;
  page.pageSize = size;
  getListm();
};

const lists = reactive<any>({
  supplieList: [],
  storeList: [],
});
const rules = {
  purchaseStorageId: [
    { required: true, message: "请选择响应仓库", trigger: ["blur", "change"] },
  ],
  supplierId: [{ required: true, message: "请选择供应商", trigger: ["blur", "change"] }],
  settlementType: [
    { required: true, message: "请选择结算类型", trigger: ["blur", "change"] },
  ],
};
const actionColumn = {
  width: 150,
  title: "操作",
  dataIndex: "action",
  slots: { customRender: "action" },
  fixed: "right",
};
// 获取用户列表
const keyWord = ref('')
const getUserList = async () => {
  const res = await getSearchUserList({ keyWord: unref(keyWord) });
  userList.value = res.map(v => {
    return {
      value: v.username,
      label: v.realname
    }
  })
}
const getList = async () => {
  const res = await getspdStoragelist({ column: "storageType", order: "asc", delFlag: 0 });
  const res2 = await suppliernoPagelist({});
  lists.supplieList = res2;
  lists.storeList = res;
  lists.storeList.forEach((v) => {
    v.label = v.storageName;
    v.value = v.id;
    v.storageNameAbbr = v.storageName + v.simpleCode;
  });
  lists.supplieList.forEach((v) => {
    v.label = v.supplierName;
    v.value = v.id;
  });
};
const reset = () => {
  formState.supplierId = undefined;
  formState.purchaseStorageId = undefined;
  page.goodsName__goodsSimpleCode__goodsCommonName__goodsCode__goodsSpecs_orFlag = ''
  formState.id = undefined;
};
const [registerModal, { setModalProps, closeModal, changeOkLoading }] = useModalInner(
  async (data) => {
    isEdit.value = data?.isEdit;
    innerData.value = [];
    // oldData = [];
    // 编辑
    if (data?.isEdit) {
      orderInfo.value.orderNo = data?.record.orderNo;
      orderInfo.value.makeTime = data?.record.makeTime;
      orderInfo.value.purchaseUser = data?.record.purchaseUser
      orderInfo.value.orderId = data?.record.id
      const res = await detailList({ orderId: data?.record.id, pageSize: -1 });
      // oldData = JSON.parse(JSON.stringify(res.records));
      innerData.value = res.records;
      innerData.value.forEach((v, i) => {
        v.index = i
        v.departRemark = v.departRemarkObj?.map(val => {
          return {
            "departInfo": {
              "value": val.departInfo.departId,
              "label": val.departInfo.departName
            },
            'goodsRemark':val.goodsRemark,
            'num': val.num
          }
        })
        if (Array.isArray(v.quantitativePackageList) === true) {
          v.packageList = v.quantitativePackageList.map(item => {
            return {
              label: item.quantitativePackageSpecs,
              value: item.id,
              quantitativePackageNum: item.quantitativePackageNum,
            }
          })
        }
      });
      formState.settlementType = data?.record?.settlementBillType + "";
      formState.supplierId = data?.record?.supplierId;
      formState.purchaseStorageId = data?.record?.purchaseStorageId;
      formState.id = data?.record?.id;
    } else { //新增
      const res = await purchasePreview({ createFlag: true })
      orderInfo.value = res.orderPreviewList[0]
      // 新增
      formState.settlementType = "2";
      reset();
    }
    tableRef.value!.addRows([{ goodsId: '-1' }]);
    innerData.value.push({ goodsId: '-1' });
    searchFlg.value = false;
    getList();
    getUserList()
    setModalProps({
      defaultFullscreen: true,
      okText: data.isEdit ? "保存采购订单" : "生成采购订单",
      title: !data?.isEdit ? "新增采购订单" : "编辑采购订单",
    });
  }
);
function setData(data) {
  let tableData = tableRef.value!.getXTable().getTableData().fullData;
  const emptyRow = tableData.find(item => item.goodsId === '-1');
  tableData = tableData.filter(item => item.goodsId !== '-1');
  data.forEach((item) => {
    tableData.push(item);
  });
  tableData.forEach((item, index) => {
    item.index = index;
  });
  if (emptyRow) {
    tableData.push(emptyRow);
  }
  // 更新数据同步
  tableRef.value!.getXTable().reloadData(tableData);
  // tableRef.value!.getXTable().refreshColumn();
}
// 历史单数据
const result = (res) => {
  res.forEach((item, index) => {
    item.fundSource_dictText = userStore.dictItems.fund_source[0].text;
    item.fundSource = userStore.dictItems.fund_source[0].value;
    // 处理规格型号
    if (item.goodsSpecsDetailOperationType === 'options') {
      item.goodsSpecsDetailOptions?.forEach((v, i, arr) => {
        arr[i] = {
          label: v,
          value: v
        };
      })
    }
    // 处理定数包规格数量
    if (item.quantitativePackageFlag == 1) {
      item.packageList = item.quantitativePackageList.map((v) => {
        return {
          label: v.quantitativePackageSpecs,
          value: v.id,
          quantitativePackageNum: v.quantitativePackageNum,
        }
      })
      item.quantitativePackageId = item.packageList[0]?.value
      item.quantitativePackageNum = item.packageList[0]?.quantitativePackageNum //数量赋值
      item.packageNum = Math.ceil(Number(item.applyNum) / Number(item.quantitativePackageNum));
      item.purchaseNum = Number(item.packageNum) * Number(item.quantitativePackageNum) ;
      item.totalAmt = (Number(item.purchaseNum) * Number(item.goodsPrice)).toFixed(2) ;
    }else{
      item.purchaseNum = item.applyNum
    }
  });
  setData(res);
};
// const handleAddGoods = () => {
//   openModal(true, formState);
// }
const handleHistory = () => {
  try {
    formRef.value
      .validate()
      .then(async () => {
        historyOrder(true, formState);
      })
      .catch((error: ValidateErrorEntity) => { });
  } catch (e) { }
};
/**
 * 选择列配置 // 由于该框架处理有bug  所以需要自己写一下事件
 */
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<any>([]);
const onSelectChange = (selectedRowKeys: (string | number)[], selectionRows) => {
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
};
const rowSelection = {
  type: "checkbox",
  columnWidth: 50,
  checkedKeys: checkedKeys,
  onChange: onSelectChange,
};
const handaleChangeInp = (props, rows) => {
  if (!formState.settlementType) { return createMessage.error("请选择结算类型"); }
  if (!formState.supplierId) { return createMessage.error("请选择供应商"); }
  if (!formState.purchaseStorageId) { return createMessage.error("请选择响应仓库"); }
  let value
  switch (props.column.key) {
    case 'goodsCode': value = rows.goodsCode
      break;
    case 'goodsName': value = rows.goodsName
      break;
    case 'goodsSpecs': value = rows.goodsSpecs
      break;
    default:
      break;
  }
  if (!value) return searchFlg.value = false
  queryData(value, page)
}
const handleCell = (event)=>{
  searchFlg.value = false;
  pageY.value = event.$event.pageY;
}
const handleInp = (e, record) => {
  record.purchaseNum = setInpLimit(record.purchaseNum, 3);
  record.totalAmt = (record.purchaseNum * record.goodsPrice).toFixed(2)
  record.packageNum = record.purchaseNum / parseInt(record.quantitativePackageNum)
}
//倍数检测
const handleBlur = (rows, event) => {
  if(!rows.purchaseNum) return createMessage.error(`请输入采购数量`);
  if (parseFloat(rows.purchaseNum) % parseInt(rows.quantitativePackageNum) !== 0) {
    rows.packageNum = Math.ceil(Number(rows.purchaseNum) / Number(rows.quantitativePackageNum));
    rows.purchaseNum = Number(rows.packageNum) * Number(rows.quantitativePackageNum) ;
    rows.totalAmt = (Number(rows.purchaseNum) * Number(rows.goodsPrice)).toFixed(2) ;
  }
}
// 定数包规格选择数量处理
const handleSelect = (record, $event) => {
  record.quantitativePackageNum = record.quantitativePackageList.find(v => v.id == $event)?.quantitativePackageNum
  record.purchaseNum = ''
  record.packageNum = ''
}
const handleFundsInfo = () => {
  if (!tableRef.value!.getSelectionData().length) return createMessage.warning("请先选择一条数据添加资金来源");
  fundsInfos(true, {
    record: {
      checkedRows: tableRef.value!.getSelectionData(),
    },
  });
};
// 表格事件
/**
 * 添加检索到的数据
 */
function addRow(record) {
  const index = tableRef.value!.getTableData().length -1
  tableRef.value!.pushRows(record,{index,setActive:false});
  nextTick(() => {
    let tbody = document.querySelector(".vxe-table--body") as any;
    let trList = tbody?.querySelectorAll(".vxe-body--row");
    (trList?.[trList.length - 2].querySelector(".ant-input") as HTMLElement).focus();
    let fixScroll = document.querySelectorAll('.fixed-right--wrapper')[1]
    fixScroll.scrollTop = tbody.scrollHeight
  });
  resetSearchData();
  searchData.value = [];
  searchFlg.value = false;  
}
// 重置表格搜索数据
function resetSearchData() {
  const len = tableRef.value!.getTableData().length -1 
  tableRef.value!.getXTable().getTableData().fullData[len]['goodsCode'] = ''
  tableRef.value!.getXTable().getTableData().fullData[len]['goodsName'] = ''
  tableRef.value!.getXTable().getTableData().fullData[len]['goodsSpecs'] = ''
  tableRef.value!.getXTable().refreshColumn();
}
function handleAdd(e, record: Recordable) { }
function handleSearchRowClick(record, index, event) {
  record.fundSource = userStore.dictItems.fund_source[0].value;
  record.fundSource_dictText = userStore.dictItems.fund_source[0].text;
  record.index = tableRef.value!.getTableData().length -1
  // 型号处理
  if (record.goodsSpecsDetailOperationType === 'options') {
    record.goodsSpecsDetailOptions.forEach((v, i, arr) => {
      arr[i] = {
        label: v,
        value: v
      };
    })
  }
  // 定数包规格处理
  if (record.quantitativePackageFlag == 1) {
    record.packageList = record.quantitativePackageList.map((v) => {
      return {
        label: v.quantitativePackageSpecs,
        value: v.id,
        quantitativePackageNum: v.quantitativePackageNum,
      }
    })
    record.quantitativePackageId = record.packageList[0]?.value
    record.quantitativePackageNum = record.packageList[0]?.quantitativePackageNum //数量赋值
  }
  addRow(record);
}
const queryData = useDebounceFn(queryDataByparams, 300);
async function queryDataByparams(value, page) {
  page.goodsName__goodsSimpleCode__goodsCommonName__goodsCode__goodsSpecs_orFlag = value ? `*${value}*` : undefined
  page.pageSize = 10;
  page.pageNo = 1;
  const result = await VoByGoodsCommon({
    ...page,
    storageId: formState.purchaseStorageId, 
    supplierId: formState.supplierId, 
    settlementType: formState.settlementType,
    applyFlag_MultiString:'0,1',//是否可请领,
    tempPurchaseFlag_MultiString:'0,1' }); // 是否临采
  searchData.value = result.records; 
  total.value = result.total;
  searchFlg.value = true;
}
function handleDel(props) {
  tableRef.value?.removeRows([props.row])
}
const handleRemarkDepart = (type,props) => { 
  //批量
  if(type === 'more'){
    if (!tableRef.value!.getSelectionData().length) return createMessage.warning("请选择需要备注的物资");
    openAddDepartRemarkModals(true, {
      record:tableRef.value!.getSelectionData(),
      type
    })
  }else{
    // 单个
    const index = props.row.index;
    openAddDepartRemarkModals(true, {
      record:props.row,
      index,
      type
    })
  }
}
const sendRes = (res) => {
  const { formData, type } = res;
  const { departInfoList, departRemarkStr } = formData
  if (type === 'one') {    
    const index = formData.index;
    tableRef.value!.getXTable().getTableData().fullData[index]['departRemark'] = departInfoList;
    tableRef.value!.getXTable().getTableData().fullData[index]['departRemarkStr'] = departRemarkStr;
  } else if (type === 'more'){
    let data = tableRef.value!.getXTable().getTableData().fullData.filter(v=> v.goodsId !== '-1')
    let selectData =  tableRef.value!.getSelectionData().filter(v=> v.goodsId !== '-1')
    data.forEach((v, i) => {
      const rowIndex = v.index; 
      const row = selectData.find(item => item.index === rowIndex);
      if (row) {
        tableRef.value!.getXTable().getTableData().fullData[rowIndex]['departRemark'] = departInfoList;
        tableRef.value!.getXTable().getTableData().fullData[rowIndex]['departRemarkStr'] = departRemarkStr;
      }
    });
  }
  tableRef.value!.getXTable().refreshColumn();
}
const getFoundsInfo = (data) => {
  const tableData = tableRef.value!.getXTable().getTableData().fullData;
  data.forEach(val => {    
    const rowIndex = val.index;
    const row = tableData.find(item => item.index === rowIndex);
    if (row) {
      row.fundSource = val.fundSource;
      row.fundSource_dictText = val.fundSource_dictText;
    }
  });
  tableRef.value?.getXTable().reloadData(tableData);
};
// 获取添加耗材数据
function setMaterialValue(data) {
  setData(data);
}

const createStrategy = (oldValueRef, formState, innerData, type, text) => ({
  handleInputChange() {
    if (oldValueRef.value && innerData.value.length > 1) {
      this.showConfirmationModal();
    }
  },
  handleClick() {
    oldValueRef.value = formState[type];
  },
  showConfirmationModal() {
    Modal.confirm({
      title: "",
      icon: createVNode(ExclamationCircleOutlined),
      content: `修改${text} 将清空耗材列表，确认是否切换？`,
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        innerData.value = [{ goodsId: "-1" }];
      },
      onCancel: () => {
        formState[type] = oldValueRef.value;
      },
    });
  },
});
// 仓库
const oldStorageId = ref();
const storageStrategy = createStrategy(
  oldStorageId,
  formState,
  innerData,
  "purchaseStorageId",
  "响应仓库"
);

const changeStore = storageStrategy.handleInputChange.bind(storageStrategy);
const handleClickStore = storageStrategy.handleClick.bind(storageStrategy);
// 供应商
const oldSupplierId = ref();
const supplierStrategy = createStrategy(
  oldSupplierId,
  formState,
  innerData,
  "supplierId",
  "供应商"
);

const changeSupplier = supplierStrategy.handleInputChange.bind(supplierStrategy);
const handleSupplierClick = supplierStrategy.handleClick.bind(supplierStrategy);

// 结算
const changeSettle = () => {
  if (formState.settlementType && innerData.value.length > 1) {
    Modal.confirm({
      title: "",
      icon: createVNode(ExclamationCircleOutlined),
      content: "修改结算类型将清空耗材列表，确认是否切换？",
      okText: "确认",
      cancelText: "取消",
      onOk() {
        innerData.value = [];
        innerData.value.push({ goodsId: "-1" });
      },
      onCancel() {
        formState.settlementType = unref(oldSettleVal);
      },
    });
  }
};
const oldSettleVal = ref();
watch(
  () => formState.settlementType,
  (newVal, oldVal) => {
    oldSettleVal.value = oldVal;
  }
);
//单据汇总
const handleSum = async () => {
  innerData.value = tableRef.value!.getXTable().getTableData().fullData //源数据
  const len = innerData.value.length
  if (len == 1) return createMessage.error("当前汇总数据为空");
  let saveData = innerData.value.filter(v => v.goodsId !== '-1').map(item => {
    return {
      "brand": item.brand,
      "fundSource": item.fundSource,
      "departRemark": item.departRemark?.map(v => {
        return {
          "departInfo": {
            "departId": v.departInfo.value,
            "departName": v.departInfo.label
          },
          'goodsRemark':v.goodsRemark,
          'num': v.num
        }
      }),
      "departRemarkStr": item.departRemarkStr,
      "goodsCode": item.goodsCode,
      "goodsId": item.goodsId,
      "goodsName": item.goodsName,
      "goodsPrice": item.goodsPrice,
      "goodsSpecs": item.goodsSpecs,
      "goodsSpecsDetail": item.goodsSpecsDetail,
      "manufacturerId": item.manufacturerId,
      "manufacturerName": item.manufacturerName,
      "packageNum": item.purchaseNum / parseInt(item.quantitativePackageNum),
      "planId": item.planId,
      "planNo": item.planNo,
      "purchaseNum": item.purchaseNum,
      "quantitativePackageFlag": item.quantitativePackageFlag,
      "quantitativePackageId": item.quantitativePackageId,
      // "remark": item.remark,
      "supplierId": item.supplierId,
      "supplierName": item.supplierName,
      "tempPurchaseFlag": item.tempPurchaseFlag,
      "tempPurchaseTime": item.tempPurchaseTime,
      "totalAmt": item.totalAmt,
      "unitName": item.unitName,
    }
  });
  const res = await collect(saveData)
  innerData.value = res
  innerData.value.forEach((v, i) => {
    v.index = i
    if (Array.isArray(v.quantitativePackageList) === true && v.quantitativePackageList.length > 0){
      v.packageList = v.quantitativePackageList.map(item => {
        return {
          label: item.quantitativePackageSpecs,
          value: item.id,
          quantitativePackageNum: item.quantitativePackageNum,
        }
      })
    }
    v.departRemark = v.departRemark?.map(val => {
      return {
        "departInfo": {
          "value": val.departInfo.departId,
          "label": val.departInfo.departName
        },
        'goodsRemark':val.goodsRemark,
        'num': val.num
      }
    })
  })
  innerData.value.push({ goodsId: '-1',index:innerData.value.length })
  tableRef.value?.getXTable().reloadData(innerData.value);
  tableRef.value!.getXTable().refreshColumn();
}
// Excel导入
const handleExcel = async () => {
  formRef.value.validate().then(async () => {
    openExportModal(true,{
      params:{
        settlementType:formState.settlementType,
        purchaseStorageId:formState.purchaseStorageId,
        supplierId:formState.supplierId,
      }
    })})
  }
const uploadRes = (res) => {
  orderInfo.value = res.documentInfo
  formState.settlementType = `${res.documentInfo.settlementType}`;
  formState.supplierId = res.documentInfo.supplierId;
  formState.purchaseStorageId = res.documentInfo?.purchaseStorageId;
  page.storageId = res.documentInfo.purchaseStorageId
  page.settlementType = res.documentInfo.settlementType
  page.supplierId = res.documentInfo.supplierId
  innerData.value = res?.detailPreviewList
  innerData.value?.forEach((v, i) => {
    v.index = i
    if (Array.isArray(v.quantitativePackageList) === true) {
      v.packageList = v.quantitativePackageList.map(item => {
        return {
          label: item.quantitativePackageSpecs,
          value: item.id,
          quantitativePackageNum: item.quantitativePackageNum,
        }
      })
    }
    v.departRemark = v.departRemark?.map(val => {
      return {
        "departInfo": {
          "value": val.departInfo.departId,
          "label": val.departInfo.departName
        },
        'goodsRemark':val.goodsRemark,
        'num': val.num
      }
    })
  })
  innerData.value.push({ goodsId: '-1',index:innerData.value.length })
  tableRef.value?.getXTable().reloadData(innerData.value);
  tableRef.value!.getXTable().refreshColumn();
}

const handleSubmit = async (type) => {
  try {
    await formRef.value.validate().then(async () => {
      changeOkLoading(true);
      let saveData = tableRef.value!.getXTable().getTableData().fullData.filter(v => v.goodsId !== '-1').map(item => {
        return {
          "brand": item.brand,
          "fundSource": item.fundSource,
          "departRemark": item.departRemark?.map(v => {
            return {
              "departInfo": {
                "departId": v.departInfo.value,
                "departName": v.departInfo.label
              },
              'goodsRemark':v.goodsRemark,
              'num': v.num
            }
          }),
          "departRemarkStr": item.departRemarkStr,
          "goodsCode": item.goodsCode,
          "goodsId": item.goodsId,
          "goodsName": item.goodsName,
          "goodsPrice": item.goodsPrice,
          "goodsSpecs": item.goodsSpecs,
          "goodsSpecsDetail": item.goodsSpecsDetail,
          "manufacturerId": item.manufacturerId,
          "manufacturerName": item.manufacturerName,
          "packageNum": item.purchaseNum / parseInt(item.quantitativePackageNum),
          "planId": item.planId,
          "planNo": item.planNo,
          "purchaseNum": item.purchaseNum,
          "quantitativePackageFlag": item.quantitativePackageFlag,
          "quantitativePackageId": item.quantitativePackageId,
          // "remark": item.remark,
          "individualFlag": item.individualFlag,
          "storageId": item.outStorageId,
          "storageName": item.outStorageName,
          "supplierId": item.supplierId,
          "supplierName": item.supplierName,
          "tempPurchaseFlag": item.tempPurchaseFlag,
          "tempPurchaseTime": item.tempPurchaseTime,
          "totalAmt": item.totalAmt,
          "unitName": item.unitName,
        }
      });
      if (isEdit.value) {
        let params = {
          ...orderInfo.value,
          settlementType: formState.settlementType,
          purchaseStorageId: formState.purchaseStorageId,
          supplierId: formState.supplierId,
          detailPreviewList: saveData,
        }
         // 编辑
       await orderEdit(params);
      } else if(type === 'create' && !isEdit.value) {
        let params = {
        preview: {
          orderPreviewList: [
            {
              ...orderInfo.value,
              settlementType: formState.settlementType,
              purchaseStorageId: formState.purchaseStorageId,
              supplierId: formState.supplierId,
              detailPreviewList: saveData,
            }
          ]
        }
      };
      // 新增保存订单
        await addOrder(params);
      }else if (type === 'push' && !isEdit.value) {
        // 新增保存推送订单
        let params = {
        preview: {
          orderPreviewList: [
            {
              ...orderInfo.value,
              settlementType: formState.settlementType,
              purchaseStorageId: formState.purchaseStorageId,
              supplierId: formState.supplierId,
              detailPreviewList: saveData,
            }
          ]
        }
      };
        await addOrder(params);
        await pushOrder({ids: [orderInfo.value.orderId]});
      }
    })
    //刷新列表
    emit("success");
    closeModal();
    // })
  } finally {
    changeOkLoading(false);
  }
};
const handleCancel = () => {
  closeModal()
}
onMounted(() => {
  window.addEventListener("click", handleMousedown); //监听鼠标按下
});

function handleMousedown(event) {
  if (event.target.parentNode.className != "scrollbar__view") return;
  page.pageNo = 1;
  page.pageSize = 10;
  searchFlg.value = false;
}
</script>

<style lang="scss" scoped>
.ml10 {
  margin-left: 10px;
}

.ant-form-inline .ant-row {
  width: 23%;
}

.check-table {
  position: absolute;
  width: 98%;
  height: 50%;
  background: #fff;
  box-shadow: 1px 1px 6px 0px black;
  z-index: 9999;
}

.boxahadow {
  display: block;
  box-shadow: 1px 1px 6px 0px black;
}

.boxnoshow {
  display: none;
  // box-shadow: 1px 1px 6px 0px black;
}

.remark {
  position: relative;

  &::after {
    position: absolute;
    left: -8px;
    content: '*';
    color: red;
    clear: both;
  }
}
.btns{
  margin-bottom: 20px;
}
</style>
