<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @cancel="handleCancel" destroyOnClose>
    <div class="dpflex aic jcc">
      <h2 style="width: 450px;text-align: left;">盘点单号：{{ inventoryCheckNo }}</h2>
    </div>
    <div class="dpflex aic jcc">
      <h2 style="width: 450px;text-align: left;">盘点仓库：{{ storageName }}</h2>
    </div>
    <div class="dpflex" style="width: 90%;margin: 0 auto;">
      <a-radio-group v-model:value="queryType" @change="handleChange">
        <a-radio :value="0">条码维度查看</a-radio>
        <a-radio :value="1">物资维度查看</a-radio>
      </a-radio-group>
    </div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <div style="width: 100%;display: flex;justify-content: space-between;align-items: center;">
          <div class="dpflex">
            <a-button type="primary" @click="handleExport">导出</a-button>
            <a-button v-auth="'pdaInventoryCount:del'" v-if="queryType === 0" class="ml-10" type="primary" @click="handleDel">删除</a-button>
            <a-button v-auth="'pdaInventoryCount:remark'" v-if="queryType === 0" class="ml-10" type="primary" @click="handleRemark">备注</a-button>
            <a-button v-auth="'pdaInventoryCount:status'" v-if="queryType === 0" class="ml-10" type="primary" @click="handleState">盘点状态</a-button>
          </div>
          <div class="total" style="width: 100%;text-align: right;">
            实盘数量合计 {{ formatIsNumber(checkTal) }}， 差异数量合计 {{ formatIsNumber(errorTal) }}， 差异金额合计 {{
              formatIsNumber(errorTalAmt) }}</div>
        </div>
      </template>
      <template #form-createName="{ model, field }">
        <ApiSelect :api="getSearchUserList" v-model:value="model[field]" allowClear showSearch placeholder="请输入盘点人"
          optionFilterProp="label" labelField="realname" valueField="realname" resultField="result"
          :params="searchParams" @search="onSearch" @click="onClick" :filterOption="false">
        </ApiSelect>
      </template>
      <template #footer>
        <div class="total" style="text-align: right">
          勾选合计：{{ '实盘数量合计 ' + totalCheck + '， 差异数量合计 ' + totalError + '， 差异金额合计 ' + totalAmount }}
        </div>
      </template>
    </BasicTable>
  </BasicModal>
  <RemarkModal :inventoryCheckId="inventoryCheckId" @success="handleSuccess" @register="RemarkModals"></RemarkModal>
  <CheckState :inventoryCheckId="inventoryCheckId" @success="handleSuccess" @register="CheckStateModals"></CheckState>
</template>

<script lang="ts" setup>
import { ref, unref,computed } from 'vue';
import { BasicTable } from '/@/components/Table';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useModal } from '/@/components/Modal';
import RemarkModal from './RemarkModal.vue';
import CheckState from './CheckState.vue';
import { useListPage } from '/@/hooks/system/useListPage'
import { detailColumns, detailCodeColumns, formState } from '../index.data'
import { detailList, inventoryExport, taskDetail, deleteTaskDetail } from '../index.api';
import { exportFile, formatIsNumber } from '/@/utils';
import { ApiSelect } from '/@/components/Form/index';
import { useApiSelect } from '/@/spdHooks/useApiSelect'
import { getSearchUserList } from '/@/api/common/api';
import { useMessage } from '/@/hooks/web/useMessage';
const { createMessage, createConfirm } = useMessage()
const { onSearch, onClick, searchParams } = useApiSelect()
const [RemarkModals, { openModal: openRemarkModal }] = useModal();
const [CheckStateModals, { openModal: openCheckStateModal }] = useModal();
const $emit = defineEmits(['close'])
const handleExport = async () => {
  const params = {
    ...getForm().getFieldsValue(),
    inventoryCheckId: unref(inventoryCheckId),
    queryType:unref(queryType),
    column: 'checkStatus,errorType,updateTime',
    order: 'asc,desc,asc'
  }
  const res = await inventoryExport(params)
  exportFile(res)
}
const queryType = ref<number>(0);
const handleChange = (e: Event) => {
  setColumns(queryType.value ? detailCodeColumns : detailColumns )
  handleSuccess()
};
const inventoryCheckNo = ref('')//盘点单号
const inventoryCheckId = ref('')//盘点单号Id
const storageName = ref('') //盘点库房名称
const checkTal = ref('') //实盘数量合计
const errorTal = ref('') //差异数量合计
const errorTalAmt = ref('') //差异金额合计
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  if (data.type === 'error') {
    getForm().setFieldsValue({ errorType: '1' })
  } else if (data.type === 'no') {
    getForm().setFieldsValue({ checkStatus: '0' })
  }
  clearSelectedRowKeys()
  queryType.value = 0
  inventoryCheckNo.value = data.record.inventoryCheckNo
  inventoryCheckId.value = data.record.inventoryCheckId ? data.record.inventoryCheckId : data.record.id
  storageName.value = data.record.storageName
  setModalProps({
    width: 1400,
    title: '盘点详情',
    footer: false,
    defaultFullscreen: true
  });
})
// 排序的字段和排序规则
let sortColumns: string[] = [];
let sortOrders: string[] = [];
const { tableContext } = useListPage({
  tableProps: {
    api: (params) => detailList({ ...params, inventoryCheckId: unref(inventoryCheckId),queryType:unref(queryType) }),
    columns: queryType ? detailColumns : detailCodeColumns,
    canResize: false,
    scroll: { y: 500 },
    showIndexColumn: true,
    showActionColumn: false,
    formConfig: {
      schemas: formState,
      showAdvancedButton: false,
      fieldMapToTime: [
        ['term', ['term_begin', 'term_end'], 'YYYY-MM-DD']
      ],
    },
    async afterFetch(T) {
      const res = await taskDetail({ inventoryCheckId: unref(inventoryCheckId), ...getForm().getFieldsValue() })
      const { checkTotal, errorTotal, errorTotalAmt } = res
      checkTal.value = checkTotal
      errorTal.value = errorTotal
      errorTalAmt.value = errorTotalAmt
    },
    sortFn(sortInfo: any | any[]) {
      sortColumns = [];
      sortOrders = [];
      if (Array.isArray(sortInfo)) {
        sortInfo.forEach((val: any) => {
          if (val.order) {
            sortColumns.push(val.columnKey);
            sortOrders.push(val.order === 'descend' ? 'desc' : 'asc');
          }
        });
      } else {
        if (sortInfo.order) {
          sortColumns.push(sortInfo.columnKey);
          sortOrders.push(sortInfo.order === 'descend' ? 'desc' : 'asc');
        }
      }
    },
    beforeFetch(v) {
      const defaultColumns = 'checkStatus,errorType,updateTime';
      const defaultOrders = 'asc,desc,asc';
      v.column = [...sortColumns, defaultColumns].join(',');
      v.order = [...sortOrders, defaultOrders].join(',');
    }
  },
})
const handleCancel = () => {
  $emit('close')
}
const handleSuccess = () => {
  reload()
  clearSelectedRowKeys()
}
const handleDel = async () => {
  if (selectedRows.value.length === 0) return createMessage.error('请选择盘点物资');
  createConfirm({
    iconType: 'warning',
    title: '',
    content: '是否删除当前所选耗材？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteTaskDetail({ ids: selectedRowKeys.value, inventoryCheckId: unref(inventoryCheckId) })
      handleSuccess()
    },
  });
}
const handleRemark = () => {
  if (selectedRows.value.length === 0) return createMessage.error('请选择盘点物资');
  openRemarkModal(true, {
    record: selectedRows.value
  });
}
const handleState = () => {
  if (selectedRows.value.length === 0) return createMessage.error('请选择盘点物资');
  const errorType = selectedRows.value[0].errorType;
  const checkStatus = selectedRows.value[0].checkStatus;
  const allStatus = selectedRows.value.every(item => item.errorType === errorType && item.checkStatus === checkStatus);
  if (!allStatus) return createMessage.error('只能批量修改盘点状态和异常状态相同的物资');
  openCheckStateModal(true, {
    record: selectedRows.value
  });
}
const calculateTotal = (field) => {
  return selectedRows.value.reduce((sum, record) => {
    if (field === 'errorAmt') {
      return Number(sum) + Number(record.errorNum || 0) * Number(record.errorAmt || 0);
    }
    return Number(sum) + Number(record[field] || 0);
  }, 0).toFixed(2);
};
const totalCheck = computed(() => calculateTotal('realNum'));
const totalError = computed(() => calculateTotal('errorNum'));
const totalAmount = computed(() => calculateTotal('errorAmt'));
const [registerTable, { reload, getForm, clearSelectedRowKeys, setColumns, }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext
</script>

<style lang="scss" scoped>
.total {
  font-size: 15px;
  color: #000;
  font-weight: 600
}
</style>
