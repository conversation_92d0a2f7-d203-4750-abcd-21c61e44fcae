<template>
  <div>
    <BasicTable bordered rowKey="id" @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="addDepartOutStore">新建科室库存出库</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"> </TableAction>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
      </div>
    </template>
    </BasicTable>
    <departOutGoodsmodel @register="AddModals" @success="success"></departOutGoodsmodel>
    <outStoreDetailModel @register="DetailModals" @success="success"></outStoreDetailModel>
  </div>
</template>

<script setup lang="ts" name="settlement-DepartOutbound">
import { BasicTable, TableAction } from "/@/components/Table";
import { useListPage } from "/@/hooks/system/useListPage";
import { useModal } from "/@/components/Modal";
import { list, revoke } from "./index.api";
import { searchFormSchema, columns } from "./index.data";
import departOutGoodsmodel from "./components/departOutGoodsmodel.vue";
import outStoreDetailModel from "./components/outStoreDetail.vue";
import { useDrawer } from "/@/components/Drawer";
import { handleSummaryNew } from '/@/utils/index'
const getFootTotal = (currentPageData)=> handleSummaryNew(currentPageData,'totalAmt','outStoreNum')
const [AddModals, { openModal: AddModal }] = useModal();
const [DetailModals, { openDrawer: DetailModal }] = useDrawer();
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: "出库单",
    api: list,
    columns,
    canResize: false,
    scroll: { y: 480 },
    showIndexColumn: true,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [],
      fieldMapToTime: [
        ["pickUpDate", ["pickUpDate_begin", "pickUpDate_end"], "YYYY-MM-DD"],
      ],
    },
    actionColumn: {
      width: 200,
      fixed: "right",
    },
    beforeFetch(params) {
      params.outStoreType = 17;
    },
  },
});
const [registerTable, { reload, clearSelectedRowKeys }] = tableContext;

const success = () => {
  reload();
  clearSelectedRowKeys();
  console.log("成功");
};

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: "查看",
      onClick: handleDetail.bind(null, record),
    },
    {
      label: "撤回",
      popConfirm: {
        title: '是否确认撤回',
        confirm: handleRetract.bind(null, record),
      },
      ifShow: (_action) => {
        return record.revokeFlag == 1;
      },
    },
  ];
}
const handleDetail = (record: Recordable) => {
  DetailModal(true, {
    record,
    isUpdate: false,
    showFooter: true,
  });
};
const handleRetract = async (record: Recordable) => {
  await revoke(
    {id:record.id}
  )
  reload();
};
const addDepartOutStore = (record: Recordable) => {
  AddModal(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
};
</script>

<style lang="scss" scoped></style>
