import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import dayjs, { Dayjs } from 'dayjs';
const ranges = {
  今天: [dayjs().startOf("day"), dayjs()],
  明天: [
    dayjs().startOf("day"),
    dayjs().startOf("day").subtract(-1, "days"),
  ],
}
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '物资编码',
    align: 'center',
    dataIndex: 'goodsCode',
    width: 140,
    resizable: true,
    fixed: 'left',
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 160,
    resizable: true,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width: 220,
    resizable: true,
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width: 150,
    resizable: true,
  },
  {
    title: '申领数量',
    align: 'center',
    dataIndex: 'requestNum',
    width: 140,
    resizable: true,
  },
  {
    title: '已发放数量',
    align: 'center',
    dataIndex: 'sendNum',
    width: 140,
    resizable: true,

  },
  {
    title: '未发放数量',
    align: 'center',
    dataIndex: 'unSendNum',
    width: 140,
    resizable: true,
  },
  {
    title: '二级库库存',
    align: 'center',
    dataIndex: 'inventoryCurrentNum',
    width: 160,
    resizable: true,
  },
  {
    title: '补货数量',
    align: 'center',
    dataIndex: '',
    width: 130,
    resizable: true,
    //补货数量=未发放数量-二级库库存，若小于0，则显示0
    customRender: ({ record }) => {
      const unSendNum = record.unSendNum;
      const inventoryCurrentNum = record.inventoryCurrentNum;
      const surplusNum = unSendNum - inventoryCurrentNum;
      return surplusNum < 0 ? 0 : surplusNum;
    },
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    width: 140,
    resizable: true,
  },


];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '手术时间',
    field: 'operation',
    component: 'RangePicker',
    required: true,
    componentProps: { valueType: 'Date', ranges: ranges },
    //默认显示昨天今天明天，三天范围
    defaultValue: [dayjs().subtract(1, 'days'), dayjs().startOf("day").subtract(-1, "days")],
  },
  {
    label: '物资编码',
    field: 'goodsCode',
    component: 'Input',
  },
  {
    label: '物资名称',
    field: 'goodsName',
    component: 'Input',
  },
  {
    label: '规格',
    field: 'goodsSpecs',
    component: 'Input',
  },
  {
    label: '型号',
    field: 'goodsSpecsDetail',
    component: 'Input',
  },

];
//详情列表数据
export const detailsColumns: BasicColumn[] = [
  {
    title: '患者编号',
    align: 'center',
    dataIndex: 'patientCode',
    width: 140,
    resizable: true,
    fixed: 'left',
  },
  {
    title: '患者姓名',
    align: 'center',
    dataIndex: 'patientName',
    width: 160,
    resizable: true,
  },
  {
    title: '申领数量',
    align: 'center',
    dataIndex: 'requestNum',
    width: 220,
    resizable: true,
  },
  {
    title: '已发放数量',
    align: 'center',
    dataIndex: 'sendNum',
    width: 150,
    resizable: true,
  },
  {
    title: '未发放数量',
    align: 'center',
    dataIndex: 'unSendNum',
    width: 140,
    resizable: true,
  },
  {
    title: '住院号',
    align: 'center',
    dataIndex: 'liveHospitalCode',
    width: 140,
    resizable: true,

  },
  {
    title: '医嘱医生',
    align: 'center',
    dataIndex: 'doctorName',
    width: 140,
    resizable: true,
  },
];