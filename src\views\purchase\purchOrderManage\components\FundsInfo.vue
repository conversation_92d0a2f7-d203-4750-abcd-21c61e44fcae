<template>
  <BasicModal @register="registerModal" @ok="ok" destroyOnClose>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { BasicTable, useTable } from '/@/components/Table';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useUserStore } from '/@/store/modules/user';
import { useMessage } from '/@/hooks/web/useMessage';
const { createMessage } = useMessage()
const userStore: any = useUserStore()
const emits = defineEmits(['getFoundsInfo'])
const tableData = ref([])
const lastTableData = ref<any>([])
const column = [
  {
    title: '资金来源编码',
    align: 'center',
    dataIndex: 'value',
  },
  {
    title: '资金来源名称',
    align: 'center',
    dataIndex: 'label',
  },
]
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  lastTableData.value = data.record.checkedRows
  checkedKeys.value = [];
  checkedRows.value = [];
  tableData.value = userStore.dictItems.fund_source
  setModalProps({
    width: 700,
    title: '资金来源',
  });
})
//表单配置
const [registerTable, { setProps }] = useTable({
  // api: detailList,
  dataSource: tableData,
  rowKey: 'value',
  bordered: true,
  columns: column,
  scroll: { y: 500 },
  pagination: false,
  showIndexColumn: false,
});
/**
  * 选择列配置 // 由于该框架处理有bug  所以需要自己写一下事件
  */
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<any>([]);
const onSelectChange = (selectedRowKeys: (string | number)[], selectionRows) => {
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
}
const rowSelection = {
  type: 'radio',
  columnWidth: 50,
  checkedKeys: checkedKeys,
  onChange: onSelectChange,
};
const ok = () => {
  if (!checkedRows.value.length) return createMessage.warning('请先选择一条数据添加资金来源')
  lastTableData.value.forEach(item => {
    if (item.goodsId !== '-1') {
      item.fundSource = checkedRows.value[0].value;
      item.fundSource_dictText = checkedRows.value[0].text;
    }
  })
  emits('getFoundsInfo', lastTableData.value)
  closeModal()
} 
</script>

<style lang="scss" scoped></style>
