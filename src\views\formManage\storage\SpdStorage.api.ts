import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/spd/spdInventory/list',
  listM = '/spd/spdInventoryDetail/list',
  listMR = '/spd/spdInventoryDetailRecord/list',
  importExcel = '/spd/spdDevice/importExcel',
  exportXls = '/spd/spdDevice/exportXls',
  //配送库房
  getDeliveryspdStoragelist = '/spd/spdStorage/queryStorageByDepartCode',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

export const listM = (params) => defHttp.get({ url: Api.listM, params });

export const listMR = (params) => defHttp.get({ url: Api.listMR, params });


/**
 * 配送库房
 * @param params
 */
export const getDeliveryspdStoragelist = (params) => defHttp.get({ url: Api.getDeliveryspdStoragelist, params });

