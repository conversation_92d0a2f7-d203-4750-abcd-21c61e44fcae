import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getspdStoragelist, suppliernoPagelist, getDepartListLeaf } from '/@/api/common/api';
import { FormatNumToThousands } from '/@/utils/index'
import { JVxeTypes } from '/@/components/jeecg/JVxeTable/types';

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '采购订单编号',
    align: 'center',
    dataIndex: 'orderNo',
    width: 200,
    resizable: true,
  },
  {
    title: '采购订单状态',
    align: 'center',
    dataIndex: 'orderStatus_dictText',
    width: 120,
    resizable: true,
  },
  {
    title: '采购计划单号',
    align: 'center',
    dataIndex: 'purchasePlanNo',
    width: 150,
    resizable: true,
  },
  {
    title: '数量',
    align: 'center',
    dataIndex: 'orderNum',
    width: 100,
    sorter: true,
    resizable: true,
  },
  {
    title: '金额',
    align: 'center',
    dataIndex: 'totalAmt',
    width: 100,
    sorter: true,
    resizable: true,
    customRender: ({ text }) => {
      return FormatNumToThousands(text);
    },
  },
  {
    title: '响应仓库',
    align: 'center',
    dataIndex: 'purchaseStorageName',
    width: 180,
    resizable: true,
  },
  {
    title: '采购订单创建人',
    align: 'center',
    dataIndex: 'purchaseUser',
    width: 150,
    resizable: true,
  },
  {
    title: '采购订单创建时间',
    align: 'center',
    dataIndex: 'makeTime',
    width: 150,
    resizable: true,
    sorter: true,
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    width: 180,
    resizable: true,
  },
  {
    title: '推送人',
    align: 'center',
    dataIndex: 'pushPerson',
    width: 100,
    resizable: true,
  },
  {
    title: '推送时间',
    align: 'center',
    dataIndex: 'pushTime',
    sorter: true,
    width: 160,
    resizable: true,
  },
  {
    title: '结算类型',
    align: 'center',
    dataIndex: 'settlementBillType_dictText',
    width: 100,
    resizable: true,
  },
  {
    title: '摘要',
    align: 'center',
    dataIndex: 'purchaseRemark',
    width:150,
    slots: { customRender: 'purchaseRemark' },
    resizable:true,
  },
];
//查询数据
export const Schema: FormSchema[] = [

  { field: 'orderNo', component: 'JInput', label: '采购订单编号', },
  {
    label: '采购科室',
    field: 'applyDepartId',
    component: 'ApiSelect',
    componentProps: {
      api: getDepartListLeaf,
      params: {
        // parentId: userStore.getTenant
      },
      showSearch: true,
      optionFilterProp: 'departNameDepartNameAbbr',
      labelField: 'departName',
      valueField: 'id',
      allowClear: true,
    },
  },
  {
    field: 'purchaseUser',
    component: 'Select',
    label: '采购订单创建人',
    slot: 'creater',
  },
  {
    field: 'makeTime',
    component: 'RangePicker',
    label: '采购订单创建时间',
    componentProps: { valueType: 'Date' },
  },
  {
    label: '结算类型',
    field: 'settlementBillType',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'settlementbills_type',
      };
    },
  },
  {
    field: 'purchasePlanNo',
    component: 'JInput',
    label: '采购计划单号',
  },
  {
    label: '采购订单状态',
    field: 'orderStatus',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'order_status',
      };
    },
  },
  {
    field: 'pushPerson',
    component: 'Select',
    label: '推送人',
    slot: 'pusher',
  },
  {
    field: 'pushTime',
    component: 'RangePicker',
    label: '推送时间',
    componentProps: { valueType: 'Date' },
  },
  {
    label: '响应仓库',
    field: 'purchaseStorageId',
    component: 'ApiSelect',
    componentProps: {
      type: 'like',
      value: [],
      api: () => getspdStoragelist({}),
      showSearch: true,
      optionFilterProp: 'storageNameAbbr',
      labelField: 'storageName',
      valueField: 'id',
      allowClear: true
    },
  },
  {
    label: '供应商',
    field: 'supplierId',
    component: 'ApiSelect',
    componentProps: {
      api: suppliernoPagelist,
      labelField: 'supplierName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: "supplierNameSupplyPycode",
    },
  },
  { field: '2', component: 'JInput', label: '物资名称', },
]

export const detailColumns: BasicColumn[] = [
  {
    title: '申领科室',
    align: 'center',
    dataIndex: 'departName',
    width: 150
  },
  {
    title: '物资编号',
    align: 'center',
    dataIndex: 'goodsCode',
    width: 180
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 150
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
  },
  {
    title: '计量单位',
    align: 'center',
    dataIndex: 'unitName',
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'goodsPrice',
  },
  {
    title: '金额  ',
    align: 'center',
    dataIndex: 'totalAmt',
  },
  {
    title: '采购数量',
    align: 'center',
    dataIndex: 'purchaseNum',
  },
  {
    title: '科室备注',
    dataIndex: 'departRemarkStr',
    width: 120,
    slots: { customRender: 'departRemarkStr' },
  },
  {
    title: '已配送数量',
    align: 'center',
    dataIndex: 'deliveryNum',
  },
  {
    title: '入库数量',
    align: 'center',
    dataIndex: 'inStoreNum',
  },
  {
    title: '剩余数量',
    align: 'center',
    dataIndex: 'remainNum',
    slots: { customRender: 'remainNum' },
  },
  // {
  //   title: '包装单位',
  //   dataIndex: 'packageUnit',
  //   width: 100,
  // },
  {
    title: '定数包规格',
    dataIndex: 'quantitativePackageSpecs',
    width: 120,
  },
  {
    title: '定数包数量',
    dataIndex: 'packageNum',
    width: 100,
  },
  {
    title: '响应仓库  ',
    align: 'center',
    dataIndex: 'outStorageName',
  },
  {
    title: '采购类型',
    align: 'center',
    dataIndex: 'purchaseAttribute_dictText',
  },
  {
    title: '需求类型  ',
    align: 'center',
    dataIndex: 'typeOfRequirement_dictText',
  },
  {
    title: '结算类型  ',
    align: 'center',
    dataIndex: 'settlementType_dictText',
  },
  {
    title: '供应商  ',
    align: 'center',
    dataIndex: 'supplierName',
  },
  {
    title: '到货日期',
    align: 'center',
    dataIndex: 'requestDate',
  },
  {
    title: '送货地址',
    align: 'center',
    dataIndex: 'deliveryAddress',
  },
  {
    title: '资金来源',
    align: 'center',
    dataIndex: 'fundSource_dictText',
  },
];
export const orderColumns= [
  {
    title: '物资编码',
    key: 'goodsCode',
    type: JVxeTypes.slot,
    width: 150,
    align: 'center',
    slotName: 'goodsCode',
  },
  {
    title: '物资名称',
    key: 'goodsName',
    type: JVxeTypes.slot,
    width: 150,
    align: 'center',
    slotName: 'goodsName',
  },
  {
    title: '规格',
    key: 'goodsSpecs',
    type: JVxeTypes.slot,
    width: 150,
    align: 'center',
    slotName: 'goodsSpecs',
  },
  {
    title: '型号',
    key: 'goodsSpecsDetail',
    type: JVxeTypes.slot,
    width: 250,
    align: 'center',
    slotName: 'goodsSpecsDetail',
  },
  {
    title: '单位',
    key: 'unitName',
    width: 80,
    align: 'center',
  },
  {
    title: '单价',
    key: 'goodsPrice',
    width: 100,
    align: 'center',
  },
  {
    title: '金额',
    key: 'totalAmt',
    width: 100,
    align: 'center',
  },
  {
    title: '采购数量',
    key: 'purchaseNum',
    type: JVxeTypes.slot,
    width: 150,
    align: 'center',
    slotName: 'purchaseNum',
  },
  {
    title: '定数包规格',
    key: 'quantitativePackageList',
    type: JVxeTypes.slot,
    width: 150,
    align: 'center',
    slotName: 'quantitativePackageList',
  },
  {
    title: '定数包数量',
    key: 'packageNum',
    type: JVxeTypes.slot,
    width: 100,
    align: 'center',
    slotName: 'packageNum',
  },
  {
    title: '物资包装单位',
    key: '1',
    width: 120,
    align: 'center',
  },
  {
    title: '物资包装数量',
    key: '2',
    width: 120,
    align: 'center',
  },
  {
    title: '科室备注',
    key: 'departRemarkStr',
    type: JVxeTypes.slot,
    width: 180,
    align: 'center',
    slotName: 'departRemarkStr',
  },
  {
    title: '供应商',
    key: 'supplierName',
    width: 150,
    align: 'center',
  },
  {
    title: '生产厂商',
    key: 'manufacturerName',
    width: 150,
    align: 'center',
  },
  {
    title: '是否临采',
    key: 'tempPurchaseFlag_dictText',
    width: 100,
    align: 'center',
  },
  {
    title: '临采上次采购时间',
    key: 'tempPurchaseTime',
    width: 150,
    align: 'center',
  },
  {
    title: '资金来源',
    key: 'fundSource_dictText',
    width: 100,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    type: JVxeTypes.slot,
    fixed: 'right',
    minWidth: 170,
    align: 'center',
    slotName: 'action',
  },
];
// export const orderColumns = [
//   {
//     title: '物资编码',
//     dataIndex: 'goodsCode',
//     width: 150,
//   },
//   {
//     title: '物资名称',
//     dataIndex: 'goodsName',
//     width: 150,
//   },
//   {
//     title: '规格',
//     dataIndex: 'goodsSpecs',
//     width: 150,
//   },
//   {
//     title: '型号',
//     dataIndex: 'goodsSpecsDetail',
//     width: 250,
//     slots: { customRender: 'goodsSpecsDetail' },
//   },
//   {
//     title: '单位',
//     dataIndex: 'unitName',
//     width: 100,
//   },
//   {
//     title: '单价',
//     dataIndex: 'goodsPrice',
//     width: 60,
//   },
//   {
//     title: '金额',
//     dataIndex: 'totalAmt',
//     width: 100,
//   },
//   {
//     title: '采购数量',
//     dataIndex: 'purchaseNum',
//     width: 120,
//     slots: { customRender: 'purchaseNum' },
//   },
//   {
//     title: '定数包规格',
//     dataIndex: 'quantitativePackageList',
//     width: 150,
//     slots: { customRender: 'quantitativePackageList' },
//   },
//   {
//     title: '定数包数量',
//     dataIndex: 'packageNum',
//     width: 100,
//     slots: { customRender: 'packageNum' },
//   },
//   {
//     title: '物资包装单位',
//     dataIndex: '2',
//     width: 120,
//   },
//   {
//     title: '物资包装数量',
//     dataIndex: '2',
//     width: 120,
//   },
//   {
//     // title: '备注',
//     dataIndex: 'departRemarkStr',
//     width: 180,
//     slots: { customRender: 'departRemarkStr', title: 'remarks' },
//   },
//   {
//     title: '供应商',
//     dataIndex: 'supplierName',
//     width: 120,
//   },
//   {
//     title: '生产厂商',
//     dataIndex: 'manufacturerName',
//     width: 120,
//   },
//   {
//     title: '是否临采',
//     dataIndex: 'tempPurchaseFlag_dictText',
//     width: 120,
//   },
//   {
//     title: '临采上次采购时间',
//     dataIndex: 'tempPurchaseTime',
//     width: 140,
//   },
//   {
//     title: '资金来源',
//     dataIndex: 'fundSource_dictText',
//     width: 100,
//   },
// ];
export const searchColumns = [
  {
    title: '物资编码',
    dataIndex: 'goodsCode',
    width: 150,
  },
  {
    title: '物资名称',
    dataIndex: 'goodsName',
    width: 150,
  },
  {
    title: '别名',
    dataIndex: 'goodsCommonName',
    width: 100,
  },
  {
    title: '规格',
    dataIndex: 'goodsSpecs',
    width: 250,
  },
  {
    title: '型号',
    dataIndex: 'goodsSpecsDetail',
    width: 100,
  },
  {
    title: '计量单位',
    dataIndex: 'unitName',
    width: 100,
  },
  {
    title: '单价',
    dataIndex: 'goodsPrice',
    width: 100,
  },
  {
    title: "品牌",
    dataIndex: "brand",
    width: 150,
  },
  {
    title: "生产厂商",
    dataIndex: "manufacturerName",
    width: 250,
  },
]

export const editColumns: BasicColumn[] = [
  {
    title: '物资编号',
    dataIndex: 'goodsCode',
    width: 150,
  },
  {
    title: '物资名称',
    dataIndex: 'goodsName',
    width: 150,
  },
  {
    title: '规格',
    dataIndex: 'goodsSpecs',
    width: 150,
  },
  {
    title: '型号',
    dataIndex: 'goodsSpecsDetail',
    width: 100,
  },
  {
    title: '单位',
    dataIndex: 'unitName',
    width: 80,
  },
  {
    title: '单价',
    dataIndex: 'goodsPrice',
    width: 60,
  },
  {
    title: '总金额',
    dataIndex: 'totalAmt',
    width: 100,
  },
  {
    title: '申领数量',
    dataIndex: 'purchaseNum',
    width: 130,
    edit: true,
    editComponent: 'InputNumber',
    editRule: async (text: any, record) => {
      console.log(text, record.purchaseNum, record.addNum);
      if (text < record.purchaseNum - record.addNum) {
        console.log(text, record.purchaseNum, record.addNum);

        return '数量需要大于原计划数量';
      }
      return '';
    },
  },
  {
    title: '定数包包装单位',
    dataIndex: 'packageUnit',
    width: 100,
  },
  {
    title: '定数包包装数量',
    dataIndex: 'packageUnit',
    width: 100,
  },
  {
    title: '物资包装单位',
    dataIndex: 'packageUnit',
    width: 100,
  },
  {
    title: '物资包装数量',
    dataIndex: 'packageUnit',
    width: 100,
  },
  {
    // title: '备注',
    dataIndex: 'departRemark',
    width: 180,
    slots: { customRender: 'departRemark', title: 'remark' },
  },
  {
    title: '供应商',
    dataIndex: 'supplierName',
    width: 120,
  },
  {
    title: '生产厂商',
    dataIndex: 'manufacturerName',
    width: 120,
  },
];