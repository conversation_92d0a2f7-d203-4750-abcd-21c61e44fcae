<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <DetailsModel @register="registerModal" @success="handleSuccess"></DetailsModel>
  </div>
</template>

<script lang="ts" name="operationGrant-ApplyStatistics" setup>

import { BasicTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage'
import { columns, searchFormSchema } from './index.data';
import { list } from './index.api';
import DetailsModel from './components/DetailsModel.vue';
import { useModal } from "/@/components/Modal/src/hooks/useModal";




const [registerModal, { openModal }] = useModal();

//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    ellipsis: true,
    scroll: { y: 500 },
    showIndexColumn: true,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
        ['operation', ['operationStartDate', 'operationEndDate'], 'YYYY-MM-DD']
      ],
    },
    actionColumn: {
      width: 120,
      fixed: 'right'
    },
    beforeFetch(params) {
      params.operationStartDate = params.operationStartDate == null ? null : params.operationStartDate + ' 00:00:00';
      params.operationEndDate = params.operationEndDate == null ? null : params.operationEndDate + ' 23:59:59';
    }
  },
})

const [registerTable, { reload }, { selectedRowKeys }] = tableContext




const handleDetail = (record) => {
  console.log(record, 'record')
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });

};
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: "详情",
      onClick: handleDetail.bind(null, record),
    },
  ];
}
/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
</script>
<style scoped>
:deep(.ant-col-4) {
  margin-left: 50%;
}
</style>
