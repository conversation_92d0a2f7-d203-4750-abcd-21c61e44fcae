<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose title="一物一码" :width="1100">
    <BasicTable @register="registerTable" :rowSelection="rowSelection" rowKey="uniqueCode">
      <template #tableTitle>
        <div style="display: flex;align-items: center;width: 100%">
          <a-radio-group v-model:value="type" @change="handleChange">
            <a-radio :value="1">唯一码</a-radio>
            <a-radio :value="2">RFID</a-radio>
          </a-radio-group>
          <a-input :disabled="type === 2" style="width: 200px" @input="checkNum" v-model:value="count" />
          <a-button class="ml10" style="border-radius: 6px;" type="primary" @click="handlePrt" :loading="buttonLoading">打印</a-button>
        </div>
        <div class="wrapper mt10" style="display: flex;width: 100%;" v-if="type === 2">
          <div class="item" v-for="item in printerList" :key="item.id">
            <img :src="printImg" alt="">
            <a-radio-group v-model:value="radioValue" @change="handleChaPrt(item)">
              <a-radio :value="item.id">{{ item.printerName }}</a-radio>
            </a-radio-group>
          </div>
        </div>
      </template>
    </BasicTable>
    <BarcodePrint ref="barcodePrint" :list="printList" :count="count"></BarcodePrint>
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicTable, useTable } from '/@/components/Table';
import { columnsMR } from '../SpdStorage.data';
import { listMR } from '../SpdStorage.api';
import BarcodePrint from './BarcodePrinter.vue'
import { useMessage } from '/@/hooks/web/useMessage';
import { getspdPrinter } from '/@/api/common/api';
import printImg from "/@/assets/images/print.png"
import axios from 'axios'
import { ref ,unref} from 'vue'
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore()
let title = ref(userStore.hospitalZoneInfo.comp.name)
const { createMessage } = useMessage();
interface RfidPrtInfo {
  interfaceUrl: string;
  printerName: string;
  sysOrgCode: string;
}
const buttonLoading = ref(false)
// 输入限制
const checkNum = (event) => {
  count.value = count.value.replace(/[^0-9.]/g, '');
  const num = count.value.split('.');
  if (num.length > 1) {
    num[1] = num[1].slice(0, 3);
    count.value = num.join('.');
    count.value = count.value.replace(/(\.\d*?[1-200])0+$/, '$1');
  }
}
const barcodePrint = ref<any>(null) //打印组件
const count = ref<any>('1') //打印次数
const type = ref(1) //打印类型
const radioValue = ref<string | undefined>('') //打印机id
const printList = ref<any>([]) // 打印数据列表
const printerList = ref<any>([]) //打印机列表
const rfidPrtInfo = ref<RfidPrtInfo>({
  interfaceUrl: '',
  printerName: '',
  sysOrgCode: '',
}) // RFID打印机信息
const isUpdate = ref(true);
const [registerModal, { setModalProps }] = useModalInner(async (data) => {
  checkedKeys.value = []
  checkedRows.value = []
  count.value = '1'
  type.value = 1
  setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  setProps({ searchInfo: { inventoryDetailId: data?.record.id } });
});
const [registerTable, { setProps }] = useTable({
  api: listMR,
  rowKey: 'id',
  bordered: true,
  columns: columnsMR,
  pagination: true,
  showIndexColumn: true,
  scroll: { y: 300 },
  showSummary: true,
});
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<Array<{ [key: string]: any }>>([])
const onSelectChange = (selectedRowKeys: (string | number)[], selectionRows) => {
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
}
const rowSelection = {
  type: 'checkbox',
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  onChange: onSelectChange,
};
const getPrinterList = async () => {
  const res = await getspdPrinter({ pageNo: 1, pageSize: 100 })
  printerList.value = res
}
const handlePrt = () => {
  if (type.value === 2 && !radioValue.value) { return createMessage.error('请选择RFID打印机'); }
  if (!checkedRows.value.length) { return createMessage.error('请选择需要打印的条码'); }
  if (type.value === 1) {
    printList.value = checkedRows.value.filter(item => typeof item === 'object' && item !== null).map(item => {
      return {
        "ItemName": item.goodsName,
        "LotID": item.batchNo,
        "ExpireDate": item.term,
        "RFIDValue": item.uniqueCode,
        "ItemStandard": item.goodsSpecs,
        "ItemManufacturer": item.manufacturerName,
        "ItemModel": item.goodsSpecsDetail,
        "BarCodeValue": item.uniqueCode
      }
    })
    setTimeout(function () {
      barcodePrint.value.print();
    }, 100);
  } else {
    printList.value = checkedRows.value.filter(item => typeof item === 'object' && item !== null).map(item => {
      return {
        "UDI":item.udiCode,
        "hospitalZoneName":unref(title),
        "ItemName": item.goodsName,
        "LotID": item.batchNo,
        "ExpireDate": item.term,
        "RFIDValue": item.rfid,
        "ItemStandard": item.goodsSpecs,
        "ItemManufacturer": item.manufacturerName,
        "ItemModel": item.goodsSpecsDetail,
        "BarCodeValue": item.uniqueCode
      }
    })
    const params = {
      PrintName: 'POSTEK TX3r',
      Labels: printList.value,
    }
    //RFID打印
    buttonLoading.value = true
    axios({
      method: 'post',
      url: rfidPrtInfo.value.interfaceUrl,
      data: JSON.stringify(params)
    }).then(res => {
      if (res.data.length) createMessage.success('打印成功')
      buttonLoading.value = false
    }).catch(error => {
      createMessage.error('打印失败')
      buttonLoading.value = false
    })
  }
}
const handleChange = () => {
  count.value = '1';
  radioValue.value = undefined;
  if (type.value === 2 && !printerList.value.length) {
    getPrinterList()
  }
}
const handleChaPrt = (record) => {
  rfidPrtInfo.value = record
}
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  .item {
    display: flex;
    justify-content: space-evenly;
    flex-direction: column;
    text-align: center;
    align-items: center;
    width: 25%;
    // margin-bottom: 10px;

    img {
      width: 30px;
      height: 30px;
    }
  }
}
</style>
