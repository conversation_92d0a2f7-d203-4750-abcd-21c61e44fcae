import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getspdStoragelist } from '/@/api/common/api';
import { h } from 'vue';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '耗材编码',
    align: 'center',
    dataIndex: 'goodsCode',
    width:120,
    resizable:true,
  },
  {
    title: '耗材名称',
    align: 'center',
    dataIndex: 'goodsName',
    width:150,
    resizable:true,
    sorter: true,
  },
  {
    title: '仓库名称',
    align: 'center',
    dataIndex: 'storageName',
    width:180,
    resizable:true,
    sorter: true,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width:120,
    resizable:true,
  },
  {
    title: '具体规格',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width:120,
    resizable:true,
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'goodsPrice',
    sorter: true,
    width:120,
    resizable:true,
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unitName',
    // sorter: true,
    width:120,
    resizable:true,
  },
  {
    title: '财务分类',
    align: 'center',
    dataIndex: 'financeCategory_dictText',
    width:120,
    resizable:true,
  },
  {
    title: '当前可用库存数量',
    align: 'center',
    dataIndex: 'currentNum',
    sorter: true,
    width:160,
    resizable:true,
  },
  {
    title: '总库存数量',
    align: 'center',
    dataIndex: 'totalNum',
    sorter: true,
    width:120,
    resizable:true,
  },
  {
    title: '锁定数量',
    align: 'center',
    dataIndex: 'lockNum',
    sorter: true,
    width:120,
    resizable:true,
  },
  {
    title: '最高库存',
    align: 'center',
    dataIndex: 'totalTop',
    width:120,
    resizable:true,
  },
  {
    title: '安全库存',
    align: 'center',
    dataIndex: 'totalSafe',
    width:120,
    resizable:true,
  },
  {
    title: '最低库存',
    align: 'center',
    dataIndex: 'totalDown',
    width:120,
    resizable:true,
  },
  {
    title: '待补充数量',
    align: 'center',
    dataIndex: 'supplementNum',
    width:120,
    resizable:true,
  },
  {
    title: '货位名称',
    align: 'center',
    dataIndex: 'locationName',
    width:150,
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    width:200,
    resizable:true,
    sorter: true,
  },
  {
    title: '厂商',
    align: 'center',
    dataIndex: 'manufacturerName',
    width:200,
    resizable:true,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '仓库',
    field: 'storageId',
    component: 'ApiSelect',
   
  },
  {
    label: '耗材名称',
    field: 'goodsName',
    component: 'JInput',
  },
  {
    label: '耗材编码',
    field: 'goodsCode',
    component: 'JInput',
  },
  {
    label: '规格',
    field: 'goodsSpecs',
    component: 'JInput',
  },
  {
    label: '具体规格',
    field: 'goodsSpecsDetail',
    component: 'JInput',
  },
  {
    label: '供应商',
    field: 'supplierName',
    component: 'JInput',
  },
  {
    label: '厂商',
    field: 'manufacturerName',
    component: 'JInput',
  },
  {
    label: '财务分类',
    field: 'financeCategory',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'db_finance_category',
        mode: "multiple", 
      };
    },
  },
  {
    label: '唯一码',
    field: 'uniqueCode',
    component: 'Input',
  },
];
//表单数据
export const columnsM: BasicColumn[] = [
  {
    title: '批次',
    align: 'center',
    dataIndex: 'batchNo',
    width: 240,
    resizable:true,
  },
  {
    title: "物资条码/定数包码",
    dataIndex: "batchCode",
    width: 240,
    align: 'center',
    resizable:true,
  },
  {
    title: "是否定数包",
    dataIndex: "quantitativePackageFlag_dictText",
    width: 160,
    align: 'center',
    resizable:true,
  },
  {
    title: "定数包规格",
    dataIndex: "quantitativePackageSpecs",
    key: "quantitativePackageSpecs",
    width: 160,
    align: 'center',
    resizable:true,
  },
  {
    title: '有效期',
    align: 'center',
    dataIndex: 'term',
    width: 160,
    resizable:true,
  },
  {
    title: '总库存数量',
    align: 'center',
    dataIndex: 'totalNum',
    width: 120,
    resizable:true,
    customRender: ({ text }) => {
      let str: any =  text-0    
      return h( () => str);
  }
  },
  {
    title: '库存总价',
    align: 'center',
    dataIndex: 'totalAmt',
    width: 120,
    resizable:true,
  },
 
];
//表单数据
export const columnsMR: BasicColumn[] = [
  {
    title: '唯一码',
    align: 'center',
    dataIndex: 'uniqueCode',
  },
  // {
  //   title: '耗材编码',
  //   align: 'center',
  //   dataIndex: 'goodsHospitalCode',
  // },
 
  {
    title: '耗材名称',
    align: 'center',
    dataIndex: 'goodsName',
  },
  // {
  //   title: '耗材udi码',
  //   align: 'center',
  //   dataIndex: 'goodsUdi',
  // },
  // {
  //   title: '耗材计费码',
  //   align: 'center',
  //   dataIndex: 'goodsAccountCode',
  // },
  // {
  //   title: '规格',
  //   align: 'center',
  //   dataIndex: 'specs',
  // },
  // {
  //   title: '具体规格',
  //   align: 'center',
  //   dataIndex: 'specDetail',
  // },
  // {
  //   title: '批次',
  //   align: 'center',
  //   dataIndex: 'batchNo',
  // },
  // {
  //   title: '单价',
  //   align: 'center',
  //   dataIndex: 'price',
  // },
  // {
  //   title: '单位',
  //   align: 'center',
  //   dataIndex: 'unitName',
  // },
  // {
  //   title: '生产日期',
  //   align: 'center',
  //   dataIndex: 'productDate',
  //   customRender: ({ text }) => {
  //     return !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
  //   },
  // },
  // {
  //   title: '有效期',
  //   align: 'center',
  //   dataIndex: 'term',
  // },
  // {
  //   title: '注册证号',
  //   align: 'center',
  //   dataIndex: 'registerNo',
  // },
  // {
  //   title: '厂商名称',
  //   align: 'center',
  //   dataIndex: 'manufacturerName',
  // },
  // {
  //   title: '供应商名称',
  //   align: 'center',
  //   dataIndex: 'supplierName',
  // },
];