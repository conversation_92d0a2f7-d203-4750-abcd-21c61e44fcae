<template>
  <div>
    <div class="wrapper">
      <div style="width: 100%;background-color: #fff;padding-top: 10px;margin:10px">
        <BasicForm @register="registerFormHeader">
        </BasicForm>
      </div>
      <div class="contenr">
        <div class="left">
          <a-button class="mt10 ml10" type="primary" v-auth="'fundProjectManagement:add'" @click="handleAdd">新增</a-button>
          <BasicTable @register="registerTable" :rowSelection="rowSelection">
          </BasicTable>
        </div>
        <div class="right">
          <div class="right-form">
            <BasicForm @register="registerForm">
            </BasicForm>
            <div class="footer">
              <a-button class="mr10" type="primary" @click="handleReset">重置</a-button>
              <a-button :loading="loading" class="mr100" type="primary" @click="handleSubmit">保存</a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <AddBasicInfoModal @register="addBasicInfoModal" @success="hancleSuccess"></AddBasicInfoModal>
</template>

<script lang="ts" setup name="cargoDispense-cargoList">
import { ref, onMounted } from 'vue';
import { BasicTable, useTable, FormSchema } from '/@/components/Table';
import { BasicForm, useForm } from "/@/components/Form/index";
import { columns, searchFormSchema } from './index.data';
import { useMessage } from '/@/hooks/web/useMessage';
import { useModal } from '/@/components/Modal';
import AddBasicInfoModal from './components/AddBasicInfoModal.vue';
const [addBasicInfoModal, { openModal: openAddBasicInfoModal }] = useModal();
const { createMessage } = useMessage();
import { list, edit } from './index.api';
onMounted(() => {
  setProps({ disabled: true })
})
const loading = ref(false);
const handleSubmit = async () => {
  try {
    loading.value = true;
    const values = await validate();
    await edit({ ...values, id: checkedKeys.value[0] });
    hancleSuccess()
  } finally {
    loading.value = false;
  }
}
const handleSearch = async () => {
  loading.value = true;
  setPagination({ current: 1, pageSize: 10 })
  hancleSuccess();
  loading.value = false;
}
const handleAdd = () => {
  openAddBasicInfoModal(true, {})
}
const hancleSuccess = () => (reload(), clearSelectedRowKeys())

const [registerFormHeader, { getFieldsValue, setFieldsValue }] = useForm({
  rowProps: { gutter: 8 },
  baseColProps: {
    xs: 24, // <576px
    sm: 12, // ≥576px
    md: 12, // ≥768px
    lg: 8, // ≥992px
    xl: 8, // ≥1200px
    xxl: 6, // ≥1600px
  },
  labelCol: {
    xs: 24,
    sm: 12,
    md: 12,
    lg: 8,
    xl: 8,
    xxl: 8,
  },
  schemas: searchFormSchema,
  submitFunc: handleSearch,
  resetFunc: async () => {
    setFieldsValue({ chargeItemsCode: undefined, chargeItemsName: undefined, isEnable: undefined, })
    resetFields()
    setPagination({ current: 1, pageSize: 10 })
    setTimeout(() => {
      hancleSuccess()
    }, 100)
  }
});
const [registerTable, { reload, clearSelectedRowKeys, setPagination }] = useTable({
  api: (params) => list({ ...params, ...getFieldsValue() }),
  showIndexColumn: true,
  columns,
  clickToRowSelect: false,
  pagination: true,
  scroll: { y: 800 },
  rowKey: 'id',
})
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<Array<string | number>>([]);
const onSelectChange = (selectedRowKeys: (string | number)[], selectionRows) => {
  setProps({ disabled: selectedRowKeys.length ? false : true })
  if (selectedRowKeys.length > 1) return createMessage.error('只允许选中一条信息');
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
  if (checkedRows.value.length === 1) {
    let { chargeItemsCode, chargeItemsName, ordinarySum, isEnable }: any = checkedRows.value[0];
    setVal({ chargeItemsCode, chargeItemsName, ordinarySum, isEnable: `${isEnable}` })
  } else {
    resetFields()
  }
}
const rowSelection = {
  type: 'checkbox',
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  onChange: onSelectChange,
};

const projectFormSchema: FormSchema[] = [
  {
    field: 'chargeItemsCode',
    component: 'Input',
    label: '收费项目编码',
    required: true,
    componentProps: {
      // disabled: !!disabled.value,
    },
  },
  {
    field: 'chargeItemsName',
    component: 'Input',
    label: '收费项目名称',
    required: true,
  },
  {
    field: 'ordinarySum',
    component: 'Input',
    label: '收费项目价格',
    required: true,
  },
  {
    field: 'isEnable',
    component: 'JDictSelectTag',
    label: '启用状态',
    required: true,
    componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
];

const [registerForm, { setFieldsValue: setVal, resetFields, validate, setProps }] = useForm({
  labelWidth: 120,
  labelCol: {
    xs: 24,
    sm: 12,
    md: 12,
    lg: 8,
    xl: 8,
    xxl: 6,
  },
  showActionButtonGroup: false,
  schemas: projectFormSchema,
});
const handleReset = () => {
  if (!checkedRows.value.length) return createMessage.error('请选择一条数据');
  let { chargeItemsCode, chargeItemsName, ordinarySum, isEnable }: any = checkedRows.value[0];
  setVal({ chargeItemsCode, chargeItemsName, ordinarySum, isEnable: `${isEnable}` })
}
</script>
<style scoped lang="scss">
.wrapper {
  width: 100%;

  .contenr {
    display: flex;
    margin: 10px;

    .left {
      width: 70%;
      margin-right: 10px;
      background-color: #fff;
    }

    .right {
      width: 30%;
      padding-top: 20px;
      background-color: #fff;

      .right-form {
        display: flex;
        // height: 100%;
        justify-content: space-around;
        flex-direction: column;
        padding-right: 20px;

        .footer {
          text-align: right;

          .mr100 {
            margin-right: 100px;
          }
        }
      }
    }
  }
}
</style>
