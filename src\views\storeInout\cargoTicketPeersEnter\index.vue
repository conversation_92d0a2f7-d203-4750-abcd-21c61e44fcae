<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="addStoreOrder">新增入库单</a-button>
        <a-button type="primary" v-if="hasPermission('goodsEnter:check')" @click="handleExam">复核</a-button>
        <a-button type="primary" @click="handleSubimts">提交</a-button>
        <!-- <a-button type="primary" v-if="hasPermission('goodsEnter:enter')" @click="handleEnter">入库</a-button> -->
        <a-button type="primary" v-if="hasPermission('goodsEnter:audit')" @click="handleAudits">审核通过</a-button>
        <a-button type="primary" v-if="hasPermission('goodsEnter:back')" @click="handleBacks">退回</a-button>
        <a-button type="primary" v-if="hasPermission('goodsEnter:revExam')" @click="handleRevExam">反复核</a-button>
        <a-button type="primary" @click="handleIFsInvoice">是否生成发票</a-button>
        <a-button type="primary" @click="handlePrintBatch">批量打印入库单</a-button>
        <a-button type="primary" v-if="hasPermission('goodsEnter:outerPrint')" @click="handlePrintOuter">批量打印出库单</a-button>
        <a-button type="primary" @click="handleAddBatchBills">添加发票</a-button>
      </template>
      <template #form-checkUser="{ model, field }">
        <ApiSelect :api="getSearchUserList" v-model:value="model[field]" allowClear showSearch placeholder="请输入验收人"
          optionFilterProp="label" labelField="realname" valueField="username" resultField="result"
          :params="searchParams" @search="onSearch" @click="onClick" :filterOption="false">
        </ApiSelect>
      </template>
      <template #form-inStoreUser="{ model, field }">
        <ApiSelect :api="getSearchUserList" v-model:value="model[field]" allowClear showSearch placeholder="请输入入库人"
          optionFilterProp="label" labelField="realname" valueField="username" resultField="result"
          :params="searchParams" @search="onSearch" @click="onClick" :filterOption="false">
        </ApiSelect>
      </template>
      <template #invoiceInfo>
        <span style="color:#1890ff;cursor: pointer;" @click="invoiceDeatil">查看</span>
      </template>
      <!-- <template #deliveryOrderNo="{ record, text }">
        <span style="color:#1890ff;cursor: pointer;" @click="handleAddbills(record)">{{ text }}</span>
      </template> -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)">
        </TableAction>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="dpflex">
            <div class="ml100">{{ `${checkSumAmount}` }}</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </div>
    </template>
    </BasicTable>
    <SpdDeliveryDrawer @register="registerDrawer" @success="handleSuccess"></SpdDeliveryDrawer>
    <div class="wrapper" ref="wrapper">
      <!-- 发票号modal -->
      <invoice-num-modal v-bind="$attrs" @register="InvoiceModal" :getContainer="() => wrapper"></invoice-num-modal>
      <!-- 移库Modal -->
      <move-modal v-bind="$attrs" @register="MoveModals" :getContainer="() => wrapper"
        @success="handleSuccess"></move-modal>
    </div>
    <!-- 打印入库单 -->
    <EnterPrint v-if="printType===1" :masterNo="masterNo" :list="enterPrintList" ref="enterPrintRef"></EnterPrint>
    <!-- 打印出库单 -->
    <OuterPrint v-if="printType===2" :list="printList" ref="outerPrintRef"></OuterPrint>
    <AddStoreOrder @register="AddStoreOrders" @success="handleSuccess"></AddStoreOrder>
    <AddBill @success="handleSuccess" @register="BillsInfos"></AddBill>
    <a-modal v-model:visible="visible" title="是否生成发票" @ok="handleOk" :closable="false">
      <div style="padding: 20px;">
          <a-radio-group v-model:value="IsInvoiceValue" name="radioGroup">
        <a-radio value="1">是</a-radio>
        <a-radio value="0">否</a-radio>
      </a-radio-group>
      </div>
    
    </a-modal>
  </div>
</template>

<script lang="ts" setup name="storeInout-cargoTicketPeersEnter">
import { ref, unref, computed,nextTick } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { ApiSelect } from '/@/components/Form/index';
import { useDrawer } from '/@/components/Drawer';
import { useListPage } from '/@/hooks/system/useListPage';
import SpdDeliveryDrawer from './components/SpdDeliveryDrawer.vue';
import { columns, searchFormSchema } from './Spdinstore.data';
import { list, queryTransferPrint, review, audit, submit, returnOrder, deleteBatch, TransferPrint,updateInvoiceGenerateStatus,queryDeliveryAndInvoices,queryTransferPrints } from './Spdinstore.api';
import { addTransferPrintTimes } from './Spdinstore.api';
import InvoiceNumModal from './components/InvoiceModal.vue';
import MoveModal from './components/MoveModal.vue';
import { useModal } from '/@/components/Modal';
import OuterPrint from '/@/components/Print/SelfGoodsPrint/outerPrint.vue';
import EnterPrint from '/@/components/Print/SelfGoodsPrint/enterPrint.vue';
import AddStoreOrder from './components/AddStoreOrder.vue'
import AddBill from './components/AddBill.vue'
import { useApiSelect } from '/@/spdHooks/useApiSelect'
import { getSearchUserList } from '/@/api/common/api';
import { useMessage } from '/@/hooks/web/useMessage';
import { usePermission } from '/@/hooks/web/usePermission';
import { message } from 'ant-design-vue';
import { handleSummaryNew, checkedSum } from '/@/utils/index'
const getFootTotal = (currentPageData)=> handleSummaryNew(currentPageData,'totalAmount','totalNum')
const checkSumAmount = computed(() => checkedSum(selectedRows.value,'totalAmount','totalNum')) // 勾选合计
const { hasPermission } = usePermission();
const { onSearch, onClick, searchParams } = useApiSelect()
const { createConfirm, createMessage } = useMessage();
const wrapper = ref(null)

//注册drawer
const [registerDrawer, { openDrawer }] = useDrawer();
const [InvoiceModal, { openModal: InvoiceOpenModal }] = useModal(); // 发票号modal
const [BillsInfos, { openModal: billsInfos }] = useModal();
const [MoveModals, { openModal: MoveOpenModal }] = useModal(); // 移库
const [AddStoreOrders, { openModal: ApplyStoreOpenModal }] = useModal(); // 新增入库
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: '配送单',
    api: list,
    columns,
    canResize: false,
    size: 'small',
    scroll: { y: 480 },
    maxHeight: 460,
    showIndexColumn: true,
    ellipsis: true,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
        ['checkDateTime', ['checkDateTime_begin', 'checkDateTime_end'], 'YYYY-MM-DD'],
        ['inStoreDateTime', ['inStoreDateTime_begin', 'inStoreDateTime_end'], 'YYYY-MM-DD'],
        ['reviewDateTime', ['reviewDateTime_begin', 'reviewDateTime_end'], 'YYYY-MM-DD'],
      ],
    },
    actionColumn: {
      width: 380,
      fixed: 'right'
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      params.checkStatus_MultiString = '1,2'
      params.settlementType = '2'
    },

  },
})
// 新增
const addStoreOrder = () => {
  ApplyStoreOpenModal(true, {
    isEdit: false,
    footer: true
  })
}
// 编辑
const editStoreOrder = (record) => {
  ApplyStoreOpenModal(true, {
    record,
    isEdit: true,
    footer: true
  })
}
// 查看
const checkStoreOrder = (record) => {
  ApplyStoreOpenModal(true, {
    record,
    isEdit: true,
    footer: false
  })
}
//复核
const handleExam = () => {
  if (!unref(selectedRowKeys).length) return createMessage.error('请选择需要复核的入库单')
  const isAudit = selectedRows.value.some(v => v.reviewStatus == 1)
  if (isAudit) return createMessage.error('当前入库单存在已复核通过的单据，请重新选择')
  createConfirm({
    iconType: 'warning',
    title: '入库单复核',
    content: '是否复核选择的入库单',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await review({ auditStatus: 1, ids: unref(selectedRowKeys) })
      handleSuccess()
    },
  });
}
// 审核
const handleAudit = async (record) => {
  if (!Boolean(record.invoiceNum)) return createMessage.error('请添加发票后进行审核')
  await audit({ ids: [record.id], auditStatus: 1 })
  handleSuccess()
}
const handleAudits = async () => {
  const isAddinvoiceNums = selectedRows.value.every(v => v.invoiceNum > 0)
  if (!unref(selectedRowKeys).length) return createMessage.error('请选择需要审核通过的入库单')
  if (!isAddinvoiceNums) return createMessage.error('存在未添加发票的单据，请重新选择')
  createConfirm({
    iconType: 'warning',
    title: '入库单审核',
    content: '是否审核通过选择的入库单',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await audit({ ids: selectedRowKeys.value, auditStatus: 1 })
      handleSuccess()
    },
  });
}
// 退回
const handleBack = async (record) => {
  await returnOrder({ ids: [record.id], auditStatus: 2 })
  handleSuccess()
}
const handleBacks = () => {
  if (!unref(selectedRowKeys).length) return createMessage.error('请选择需要退回的入库单')
  const isBacks = selectedRows.value.some(v => v.auditStatus == 2)
  if (isBacks) return createMessage.error('存在已退回的入库单,请勿重新退回')
  const isHaveInStoreNo = selectedRows.value.some(v => Boolean(v.inStoreNo))
  if (isHaveInStoreNo) return createMessage.error('存在已入库的入库单,请重新选择')
  createConfirm({
    iconType: 'warning',
    title: '入库单退回',
    content: '是否退回选择的入库单',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await returnOrder({ ids: unref(selectedRowKeys), auditStatus: 2 })
      handleSuccess()
    },
  });
}
// 提交
const handleSubimt = async (record) => {
  await submit({ ids: [record.id], status: 1 })
  handleSuccess()
}
const handleSubimts = () => {
  if (!unref(selectedRowKeys).length) return createMessage.error('请选择需要提交的入库单')
  const isSubmits = selectedRows.value.some(v => v.submitStatus == 1)
  if (isSubmits) return createMessage.error('存在已提交的入库单,请勿重新提交')
  createConfirm({
    iconType: 'warning',
    title: '入库单提交',
    content: '是否提交选择的入库单',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await submit({ status: 1, ids: unref(selectedRowKeys) })
      handleSuccess()
    },
  });
}
// 撤回
const handleRevoke = async (record) => {
  await submit({ ids: [record.id], status: 2 })
  handleSuccess()
}
const handleRevExam = () => {
  if (!unref(selectedRowKeys).length) return createMessage.error('请选择需要反复核的入库单')
  const isAudit = selectedRows.value.some(v => v.reviewStatus == 0)
  if (isAudit) return createMessage.error('存在待复核的单据,请复核后进行反复核')
  createConfirm({
    iconType: 'warning',
    title: '入库单反复核',
    content: '是否反复核选择的入库单',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await review({ auditStatus: 0, ids: unref(selectedRowKeys) })
      handleSuccess()
    },
    
  });
}

//批量打印出库单
const handlePrintOuter = async ()=>{
  if (!unref(selectedRowKeys).length) return createMessage.error('请选择需要打印的单据!')
  const basisNos = selectedRows.value.map(item => item.deliveryOrderNo)
  const res = await queryTransferPrints({pageSize:6,basisNos})
  printList.value = res
  console.log(outerPrintRef.value,'outerPrintRef.value');
  nextTick(()=>{
    
  })
  setTimeout(function () {
    outerPrintRef.value.print();
  }, 300);
}  

const hasField = (arr)=> {
  if (arr.length <= 1) return false; 
  const seen = new Set();
  for (const item of arr) {
    seen.add(item.supplierId);
  }
  return seen.size > 1;
}
//批量添加发票
const handleAddBatchBills = async ()=>{
  if(!selectedRows.value.length) return createMessage.error('请选择需要添加发票的单据')
  if(hasField(selectedRows.value)) return createMessage.error('需要开票的单据存在不同的供应商，请重新选择')
  const res = await queryDeliveryAndInvoices({ids: selectedRowKeys.value.join(',')})
    billsInfos(true, {
    record:res,
    isUpdate: false,
    showFooter: false,
  });
}
//删除
const handleDel = async (record) => {
  await deleteBatch({ ids: record.id })
  handleSuccess()
}
const [registerTable, { reload, clearSelectedRowKeys }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext
/**
   * 操作栏
   */
function getTableAction(record) {
  return [
    {
      label: '入库',
      onClick: storage.bind(null, record, 'enter'),
      ifShow: (_action) => {
        return (record.inStoreStatus == 0 || record.inStoreStatus == 1) && record.endFlag != 1 && record.auditStatus != 0;
      },
    },
    // manualAddFlag:1 手动添加   0 院外推送
    {
      label: '编辑',
      onClick: editStoreOrder.bind(null, record),
      ifShow: (_action) => record.auditStatus == 0 && record.manualAddFlag == 1 && record.submitStatus != 1
    },
    {
      label: '提交',
      popConfirm: {
        title: '是否确认提交',
        confirm: handleSubimt.bind(null, record),
      },
      ifShow: (_action) => record.submitStatus != 1 && record.auditStatus == 0
    },
    {
      label: '审核',
      ifShow: record.auditStatus == 0 && record.submitStatus == 1,
      auth: 'super:enterAudit',
      popConfirm: {
        title: '是否确认审核',
        confirm: handleAudit.bind(null, record),
      }
    },
    {
      label: '退回',
      ifShow: record.auditStatus == 1 && !record.inStoreNo,
      popConfirm: {
        title: '是否确认退回',
        confirm: handleBack.bind(null, record),
      }
    },
    {
      label: '撤回',
      popConfirm: {
        title: '是否确认撤回',
        confirm: handleRevoke.bind(null, record),
      },
      ifShow: (_action) => record.submitStatus == 1 && record.auditStatus == 0
    },
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDel.bind(null, record),
      },
      ifShow: (_action) => record.submitStatus == 0 || record.submitStatus == 2
    },
    // {
    //   label: '添加发票',
    //   onClick: handleAddbills.bind(null, record),
    //   //ifShow: (_action) => record.auditStatus != 1
    // },
    {
      label: '发票查看',
      onClick: billsDetail.bind(null, record),
    },
    {
      label: '移库',
      onClick: move.bind(null, record,),
      auth: 'material:move',
      ifShow: (_action) => {
        return record.moveStoreFlag !== 1 && record.inStoreStatus == 2
      },
    },
    {
      label: '查看',
      onClick: checkStoreOrder.bind(null, record),
    },
    {
      label: '打印出库单',
      onClick: handlePrint.bind(null, record),
      ifShow: (_action) => {
        return record.moveStoreFlag == 1;
      },
    },
    {
      label: '打印入库单',
      onClick: getEnterPrint.bind(null, record),
      ifShow: (_action) => {
        return record.inStoreStatus == 2;
      },
    },
    // {
    //   label: '打印条码',
    //   onClick: barcodePrint.bind(null, record),
    // },
  ]
}
function handleSuccess() {
  reload()
  clearSelectedRowKeys()
}
const invoiceDeatil = (record: Recordable) => {
  InvoiceOpenModal(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
}
// const handleAddbills = (record: Recordable) => {
//   //if(record.inStoreNo) return createMessage.error('已入库无法继续添加发票')
//   billsInfos(true, {
//     record,
//     isUpdate: false,
//     showFooter: false,
//   });
// }
/**
  * 入库查看详情
 */
//const lookDetail = (record: Recordable, type) => {
//  record.type = type
//  openDrawer(true, {
//    record,
//    isUpdate: false,
//    showFooter: false,
//  });
//}
// 移库
const move = async (record: Recordable) => {
  MoveOpenModal(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
  // await moveStore({ deliveryId: record.id, pushFlag: 1 },);
  // handleSuccess()
}
// 入库
const storage = (record: Recordable, type) => {
  record.type = type
  openDrawer(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
}
const billsDetail = (record) => {
  InvoiceOpenModal(true, {
    record
  })
}

const visible = ref<boolean>(false);
const IsInvoiceValue = ref<any>('1');
//是否生成发票
const handleIFsInvoice = () => {
  if (selectedRowKeys.value.length == 0) return createMessage.error('请选择单据')
  visible.value = true;
}
const handleOk = async () => {
  let params = {
    status: IsInvoiceValue.value,
    ids: selectedRowKeys.value,
  }
  await updateInvoiceGenerateStatus(params)
  visible.value = false;
  reload()
  selectedRowKeys.value = []
};

const handlePrintBatch =async()=>{
  let selectedData =selectedRows.value
  let record={
    inStoreNos:[]
  }
  selectedData.forEach(item => {
    if (!item.inStoreNo) {
      message.warn('当前所选单据中存在未生成入库单数据，请重新选择')
    }
    record.inStoreNos.push(...item.inStoreNos)
  });
  getEnterPrint(record)
}
//打印入库单
const printType = ref(0)
const masterNo = ref()
const enterPrintList = ref<any>([])
const enterPrintRef = ref<any>(null)
const getEnterPrint = async (record) => {
  printType.value = 1
  masterNo.value = record.inStoreNo
  let params = <any>{
    inStoreNos: record.inStoreNos,//依据单号
    size: 6,// size
  }
  const res = await TransferPrint(params)
  enterPrintList.value = res
  setTimeout(function () {
    enterPrintRef.value.print();
  }, 100);
  await addTransferPrintTimes({ids:[res[0]?.id]})
  reload()
}



//打印出库单
const printList = ref<any>([])
const outerPrintRef = ref<any>(null)
const handlePrint = async (record) => {
  printType.value = 2
  let params = <any>{
    basisNo: record.deliveryOrderNo,//依据单号
    pageSize: 6,// pageSize
  }
  const res = await queryTransferPrint(params)
  printList.value = res
  setTimeout(function () {
    outerPrintRef.value.print();
  }, 100);
}




</script>
<style scoped lang="scss"></style>