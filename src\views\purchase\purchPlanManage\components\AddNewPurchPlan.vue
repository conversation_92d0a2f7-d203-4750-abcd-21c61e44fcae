<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit" destroyOnClose :maskClosable="false">
    <div v-if="!orderList.length" class="outer-plan">
      <h1 style="text-align: center; font-size: 18px;font-weight: 600">采购计划单号：{{ orderInfo.documentNo }}</h1>
      <div class="dpflex jcc aic">
        <div>采购计划单创建人:{{ orderInfo.createUserRealname }}</div>
        <div style="margin-left: 100px;">采购计划单创建时间:{{ orderInfo.createTime }}</div>
      </div>
      <a-form style="height: 80px" ref="formRef" :model="page" :rules="rules" layout="inline"
        :label-col="{ style: { width: '150px' } }" :wrapper-col="{ span: 20 }">
        <a-form-item label="响应仓库" name="storageId">
          <a-select :disabled="formState.isEdit" @change="changeVal" @click="handleClick" v-model:value="page.storageId"
            placeholder="请选择响应仓库" style="width: 220px" allowClear showSearch optionFilterProp="storageNameAbbr"
            :options="formState.storeList">
          </a-select>
        </a-form-item>
      </a-form>          
      <div class="btns">
        <a-button class="ml10" type="primary" @click="handaleAddGoods">添加耗材</a-button>
        <a-button class="ml10" type="primary" @click="handleApply">选择申领单</a-button>
        <a-button class="ml10" type="primary" @click="handleSum">选择汇总单</a-button>
        <a-button class="ml10" type="primary" @click="handleFundsInfo">资金来源</a-button>
        <a-button class="ml10" type="primary" @click="handleRemarkDepart('more',null)">批量备注科室</a-button>
        <a-button class="ml10" type="primary" @click="handleSumOrder">单据汇总</a-button>
        <a-button class="ml10" type="primary" v-auth="'purchPlanManage:importExcel'" @click="handleExcel">Excel导入</a-button>
        <a-button class="ml10" type="primary" v-auth="'purchPlanManage:safe'" @click="handleAction('safe')">一键补足安全库存</a-button>
        <a-button class="ml10" type="primary" v-auth="'purchPlanManage:top'" @click="handleAction('top')">一键补足库存上限</a-button>
      </div>
      <JVxeTable id="saveTable" ref="tableRef" stripe rowNumber rowSelection resizable asyncRemove :toolbarConfig="false"
        :maxHeight="480" :checkboxConfig="{ range: true }" :disabledRows="{ input: ['text--16', 'text--18'] }"
        :loading="false" :disabled="false" :columns="planColumns" :dataSource="innerData" :toolbar="false" @cell-click="handleCell">
        <template #goodsCode="prpos">
          <a-input v-if="prpos.row.goodsId === '-1'" v-model:value="prpos.row.goodsCode"
            @focus="handaleChangeInp(prpos, prpos.row)" @change="handaleChangeInp(prpos, prpos.row)" />
          <span v-else>{{ prpos.row.goodsCode }}</span>
        </template>
        <template #goodsName="prpos">
          <a-input v-if="prpos.row.goodsId === '-1'" v-model:value="prpos.row.goodsName"
            @focus="handaleChangeInp(prpos, prpos.rowNumber)" @change="handaleChangeInp(prpos, prpos.row)" />
          <span v-else>{{ prpos.row.goodsName }}</span>
        </template>
        <template #goodsSpecs="prpos">
          <a-input v-if="prpos.row.goodsId === '-1'" v-model:value="prpos.row.goodsSpecs"
            @focus="handaleChangeInp(prpos, prpos.row)" @change="handaleChangeInp(prpos, prpos.row)" />
          <span v-else>{{ prpos.row.goodsSpecs }}</span>
        </template>

        <template #purchaseNum="prpos">
          <a-input v-if="prpos.row.goodsId !== '-1' && prpos.row.quantitativePackageFlag == 1"
            @blur="handleBlur(prpos.row, $event)" @input="handleInp($event, prpos.row)"
            v-model:value="prpos.row.purchaseNum" placeholder="请输入数量" />
          <a-input v-if="prpos.row.goodsId !== '-1' && prpos.row.quantitativePackageFlag != 1"
            @input="handleInp($event, prpos.row)" v-model:value="prpos.row.purchaseNum" placeholder="请输入数量" />
        </template>
        <template #departRemarkStr="prpos">
          <a-input disabled v-model:value="prpos.row.departRemarkStr" v-if="prpos.row.goodsId != '-1'">
            <template #suffix>
              <a-popover v-model:visible="prpos.row.visible" trigger="click" placement="right">
                <template #content>
                  <a-textarea disabled v-model:value="prpos.row.departRemarkStr" placeholder="" :rows="8" allow-clear/>
                </template>
                <fullscreen-outlined />
              </a-popover>
            </template>
          </a-input>
        </template>
        <!-- 定数包规格 -->
        <template #quantitativePackageList="prpos">
          <a-select v-if="prpos.row.quantitativePackageFlag == 1" @change="handleSelect(prpos.row, $event)"
            style="width: 200;" v-model:value="prpos.row.quantitativePackageId" :options="prpos.row.packageList">
          </a-select> 
        </template>
        <!-- 定数包数量 -->
        <template #packageNum="prpos">
          <span v-if="prpos.row.quantitativePackageFlag == 1">{{ prpos.row.packageNum }}</span>
        </template>
        <template #goodsSpecsDetail="prpos">
          <!-- options 展示列表  readOnly 不可编辑 -->
          <a-select v-if="prpos.row.goodsSpecsDetailOperationType === 'options'" style="width: 220px;"
            v-model:value="prpos.row.goodsSpecsDetail" :options="prpos.row.goodsSpecsDetailOptions">
          </a-select>
          <span v-else>{{ prpos.row.goodsSpecsDetail }}</span>
        </template>

        <template #action="props">
          <a-button v-if="props.row.goodsId != '-1'" type="link" @click="handleRemarkDepart('one',props)">备注科室</a-button>
          <a-button v-if="props.row.goodsId != '-1'" type="link" @click="handleDel(props)">
            <template #icon><delete-outlined /></template>删除
          </a-button>
        </template>
      </JVxeTable>
      <div class="check-table" :style="getstyle" ref="dragElement" @mousedown="mousedownFunction"
        :class="searchFlg ? 'boxahadow' : 'boxnoshow'">
        <BasicTable :loading="loading" v-show="searchFlg" :show-action-column="false" bordered size="middle"
          rowKey="goodsId" :canResize="false" :columns="searchColumns" :dataSource="searchData" :pagination="false"
          :scroll="{ y: 300 }" @row-click="handleSearchRowClick" :ellipsis="true">
          <template #action="{ record }">
            <TableAction :actions="[
              {
                label: '添加',
                icon: 'ic:outline-delete-outline',
              },
            ]" />
          </template>
          <template #footer>
            <div class="page" style="text-align: right">
              <Pagination @change="changePage" @showSizeChange="changeSize" :defaultPageSize="10"
                v-model:current="page.pageNo" v-model:pageSize="page.pageSize" size="small" :total="total"
                :pageSizeOptions="['10', '50', '100', '500']" :show-total="(total) => `共 ${total} 条数据`"
                show-size-changer show-quick-jumper />
            </div>
          </template>
        </BasicTable>
      </div>
    </div>
    <!-- 订单列表 -->
    <div v-if="orderList.length" class="order">
      <a-table @expand="expandFun" :expandedRowKeys="expandedRowKeys" :scroll="{ x: '100%' }" :columns="orderColumns"
        :data-source="orderList" rowKey="orderId" :pagination="false">
        <template #expandedRowRender="{ record }">
          <a-table :scroll="{ x: '100%' }" v-if="record.detailPreviewList.length" style="width: 100%;"
            :columns="orderChildColumns" :data-source="record.detailPreviewList" :pagination="false"></a-table>
        </template>
      </a-table>
    </div>
    <ApplyOrder @register="ApplyOrders" @result="result"></ApplyOrder>
    <SumOrder @register="SumOrders" @result="result"></SumOrder>
    <FundsInfo @getFoundsInfo="getFoundsInfo" @register="FundsInfos"></FundsInfo>
    <AddDepartRemarkModal @send-res="sendRes" @register="AddDepartRemarkModals"></AddDepartRemarkModal>
    <SelectDepartGoods @result="result" @register="SelectDepartGoodss"></SelectDepartGoods>
    <ExportModal @register="exportModals" @upload-Res="uploadRes"></ExportModal>
    <template #appendFooter>
      <a-button v-auth="'purchPlanManage:createOrder'" :loading="createOrderLoading" v-if="!exitflag && !orderList?.length" type="primary" @click="handleOrder">生成采购订单</a-button>
      <a-button v-auth="'purchPlanManage:saveOrder'" :loading="saveLoading" v-if="orderList?.length" type="primary" @click="handleSavePush('save')">保存订单</a-button>
      <a-button v-auth="'purchPlanManage:savePushOrder'" :loading="pushLoading" v-if="orderList?.length" type="primary" @click="handleSavePush('push')">保存采购订单并推送</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, unref, nextTick } from "vue";
import { BasicTable, TableAction } from "/@/components/Table";
import { BasicModal, useModalInner } from "/@/components/Modal";
import { getspdStoragelist, VoByGoodsCommon } from "/@/api/common/api";
import { useModal } from "/@/components/Modal";
import ApplyOrder from "./ApplyOrder.vue";
import SumOrder from "./SumOrder.vue";
import FundsInfo from "./FundsInfo.vue";
import SelectDepartGoods from "./SelectDepartGoods.vue";
import AddDepartRemarkModal from './AddDepartRemark.vue';
import ExportModal from './exportModal.vue';
import { planColumns, searchColumns, orderChildColumns, orderColumns } from "../index.data";
import { addPlan, listByPurchasePlanId, editPlan, purchaseDetaiPreview, purchasePreview, collect, add, fillInventory } from "../index.api";
import { pushOrder } from "../../purchOrderManage/index.api";
import { useDebounceFn } from "@vueuse/core";
import { useMessage } from "/@/hooks/web/useMessage";
import { Pagination } from "ant-design-vue";
import { FullscreenOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { setInpLimit } from '/@/utils';
import { JVxeTableInstance } from '/@/components/jeecg/JVxeTable/types';
import { useRouter } from 'vue-router';
const router = useRouter();
const { createMessage, createConfirm } = useMessage();
import { useUserStore } from "/@/store/modules/user";
import { useDraggable } from '/@/spdHooks/useDraggable'
import { log } from "console";
const { dragElement, mousedownFunction } = useDraggable();
const userStore: any = useUserStore();
const [ApplyOrders, { openModal }] = useModal();
const [SumOrders, { openModal: sumOrder }] = useModal();
const [FundsInfos, { openModal: fundsInfos }] = useModal();
const [AddDepartRemarkModals, { openModal: openAddDepartRemarkModals }] = useModal();
const [SelectDepartGoodss, { openModal:openModalSelectDepartGoods }] = useModal();
const [exportModals, { openModal: openExportModal }] = useModal();

const tableRef = ref<JVxeTableInstance>(); // vxe表格实例

const emit = defineEmits(["success"]);
const innerData = ref<any[]>([]);
const searchData = ref<any[]>([]); //子表格data
const searchFlg = ref(false); //子表格显隐
const loading = ref(false);
const formRef = ref();
const formState = reactive<any>({
  purchasePlanId: undefined, // 采购计划id
  isEdit: false, // 是否编辑
  storeList: [], //仓库列表
});
const isJump = ref(); //是否跳转过来的
// 分页搜索传参
const page = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  storageId: undefined,
  applyFlag_MultiString:'0,1',//是否可请领,
  tempPurchaseFlag_MultiString:'0' // 是否临采
});
//单号信息
const orderInfo = ref<any>({
  createTime: '',//创建时间
  createUserRealname: '',//创建人
  createUsername: '',//创建人工号
  departId: '',//
  departName: '',//
  documentNo: '', //单号
  documentId: '',//单据id
  hospitalZoneId: ''
})
const orderList = ref([]) //订单列表
const ids = ref() //订单id列表
const purchaseFlag = ref() //1科室采购 2库房直接采购
const saveLoading = ref(false)
const pushLoading = ref(false)
const createOrderLoading = ref(false)
const exitflag = ref(0)
const reset = () => {
  page.storageId = undefined;
  page.goodsName__goodsSimpleCode__goodsCommonName__goodsCode__goodsSpecs_orFlag = ''
  orderInfo.value = {}
};
const total = ref(0);
const pageY = ref(0)
const getstyle = computed(() => {
  if (pageY.value > 600) {
    return { bottom: document.documentElement.clientHeight - pageY.value + "px", top: 'unset' };
  } else {
    return { top: -30 + pageY.value + "px" };
  }
});
const getListm = () => {
  loading.value = true;
  VoByGoodsCommon(page).then((res) => {
    total.value = res.total;
    searchData.value = res.records;
    loading.value = false;
  });
};
const changePage = (pag, pageSize) => {
  page.pageNo = pag;

  getListm();
};
const changeSize = (current, size) => {
  page.pageNo = 1;
  page.pageSize = size;
  getListm();
};

const rules = {
  storageId: [{ required: true, message: "请选择响应仓库", trigger: ["blur", "change"] }],
};
const getStoreList = async () => {
  const res = await getspdStoragelist({ column: "storageType", order: "asc", delFlag: 0, storageType:1 });
  formState.storeList = res;
  formState.storeList.forEach((v) => {
    v.label = v.storageName;
    v.value = v.id;
    v.storageNameAbbr = v.storageName + v.simpleCode;
  });
};
const [registerModal, { setModalProps, closeModal }] = useModalInner(
  async (data) => {
    setModalProps({
      defaultFullscreen: true,
      okText: data.isEdit ? "保存采购计划" : "生成采购计划",
      title: "采购计划编辑",
      showOkBtn:true,
      confirmLoading:false
    });
    searchFlg.value = false;
    getStoreList();
    reset();
    orderList.value = [];
    innerData.value = [];
    formState.isEdit = data.isEdit;
    isJump.value = data?.isJump
    purchaseFlag.value = data?.record?.purchaseFlag
    // 新增单号处理
    if (!data.isEdit && data.isJump != '1') {
      const res = await await purchaseDetaiPreview({ createFlag: true });
      orderInfo.value = res.documentInfo
      purchaseFlag.value = 2
      exitflag.value = 0
    }
    //直接编辑
    if (data.isJump === '1') {
      page.storageId = data.orderInfo.documentInfo.storageId // 仓库id
      orderInfo.value = data.orderInfo.documentInfo
      innerData.value = data.orderInfo.detailList;
      tableRef.value?.getXTable().reloadData(innerData.value);
      purchaseFlag.value = 1
    } else if (data.isEdit) {  //普通编辑
      orderInfo.value.documentNo = data.record.purchasePlanNo
      orderInfo.value.documentId = data.record.id
      orderInfo.value.createUserRealname = data.record.makeUserRealname
      orderInfo.value.createTime = data.record.createTime
      page.storageId = data.record.storageId;
      formState.purchasePlanId = data.record.id;
      const res = await listByPurchasePlanId({ purchasePlanId: data.record.id, pageSize: '-1' });
      exitflag.value = res.records[0].exitflag
      innerData.value = res.records;
      innerData.value.forEach((v, i) => {
        v.index = i
        v.departRemark = v.departRemarkObj?.map(val => {
          return {
            "departInfo": {
              "value": val.departInfo.departId,
              "label": val.departInfo.departName
            },
            'goodsRemark':val.goodsRemark,
            'num': val.num
          }
        })
        if (Array.isArray(v.quantitativePackageList) === true) {
          v.packageList = v.quantitativePackageList.map(item => {
            return {
              label: item.quantitativePackageSpecs,
              value: item.id,
              quantitativePackageNum: item.quantitativePackageNum,
            }
          })
        }
      })
    } else {
      oldStorageId.value = undefined;
    }
    // tableRef.value!.addRows([{ goodsId: '-1' }]);
    innerData.value.push({ goodsId: '-1' });
    tableRef.value?.getXTable().reloadData(innerData.value);
  }
);
function setData(data) {
  let tableData = tableRef.value!.getXTable().getTableData().fullData;
  const emptyRow = tableData.find(item => item.goodsId === '-1');
  tableData = tableData.filter(item => item.goodsId !== '-1');
  data.forEach((item) => {
    tableData.push(item);
  });
  tableData.forEach((item, index) => {
    item.index = index;
  });
  if (emptyRow) {
    tableData.push(emptyRow);
  }
  // 更新数据同步
  tableRef.value!.getXTable().reloadData(tableData);
  // tableRef.value!.getXTable().refreshColumn();
}
const result = (res) => {
  res.forEach((item, index) => {
    item.fundSource_dictText = userStore.dictItems.fund_source[0].text;
    item.fundSource = userStore.dictItems.fund_source[0].value;
    // 处理规格型号
    if (item.goodsSpecsDetailOperationType === 'options') {
      item.goodsSpecsDetailOptions?.forEach((v, i, arr) => {
        arr[i] = {
          label: v,
          value: v
        };
      })
    }
    // 处理定数包规格数量
    if (item.quantitativePackageFlag == 1) {
      item.packageList = item.quantitativePackageList.map((v) => {
        return {
          label: v.quantitativePackageSpecs,
          value: v.id,
          quantitativePackageNum: v.quantitativePackageNum,
        }
      })
      item.quantitativePackageId = item.packageList[0]?.value
      item.quantitativePackageNum = item.packageList[0]?.quantitativePackageNum //数量赋值
      item.packageNum = Math.ceil(Number(item.applyNum) / Number(item.quantitativePackageNum));
      item.purchaseNum = Number(item.packageNum) * Number(item.quantitativePackageNum) ;
      item.totalAmt = (Number(item.purchaseNum) * Number(item.goodsPrice)).toFixed(2) ;
    }else{
      item.purchaseNum = item.applyNum
    }
  });
  setData(res);
};
const handleApply = async () => {
  try {
    formRef.value
      .validate()
      .then(() => {
        openModal(true, page);
      })
      .catch((error) => {
      });
  } catch (e) {
    console.log(e);
  }
};
//选择耗材
const handaleAddGoods = () => {
  formRef.value
    .validate()
    .then(() => {
      openModalSelectDepartGoods(true, {
      storageId: page.storageId
    });
  })
}
const handleSum = async () => {
  try {
    formRef.value
      .validate()
      .then(() => {
        sumOrder(true, page);
      })
      .catch((error) => {
        // console.log("error", error);
      });
  } catch (e) {
    console.log(e);
  }
};
const handleFundsInfo = () => {
  if (!tableRef.value!.getSelectionData().length)
    return createMessage.warning("请先选择一条数据添加资金来源");
  fundsInfos(true, {
    record: {
      checkedRows: tableRef.value!.getSelectionData(),
    },
  });
};
const oldStorageId = ref();
function changeVal(e) {
  let len = tableRef.value!.getXTable().getTableData().fullData.length
  if (len == 1) return;
  createConfirm({
    iconType: "warning",
    title: "确认切换响应仓库",
    content: "切换响应仓库，添加的耗材将会被清空！",
    okText: "确认",
    cancelText: "取消",
    onOk: () => {
      innerData.value = [];
      innerData.value.push({ goodsId: '-1' });
    },
    onCancel: () => {
      page.storageId = oldStorageId.value;
    },
  });
}
const handleClick = () => {
  oldStorageId.value = page.storageId;
};
const actionColumn = {
  width: 140,
  title: "操作",
  dataIndex: "action",
  slots: { customRender: "action" },
  fixed: "right",
};

/**
 * 添加检索到的数据
 */
function addRow(record) {
  tableRef.value!.pushRows(record,{index:record.index,setActive:false});
  const l = tableRef.value!.getXTable().getTableData().fullData.length - 1
  tableRef.value!.getXTable().getTableData().fullData[l]['index'] = l
  nextTick(() => {
    let tbody = document.querySelector(".vxe-table--body") as any;
    let trList = tbody?.querySelectorAll(".vxe-body--row");
    (trList?.[trList.length - 2].querySelector(".ant-input") as HTMLElement).focus();
    let fixScroll = document.querySelectorAll('.fixed-right--wrapper')[1]
    fixScroll.scrollTop = tbody.scrollHeight
  });
  resetSearchData();
  searchData.value = [];
  searchFlg.value = false;  
}
// 重置搜索值
function resetSearchData() {
  const len = tableRef.value!.getTableData().length -1 
  tableRef.value!.getXTable().getTableData().fullData[len]['goodsCode'] = ''
  tableRef.value!.getXTable().getTableData().fullData[len]['goodsName'] = ''
  tableRef.value!.getXTable().getTableData().fullData[len]['goodsSpecs'] = '' 
  tableRef.value!.getXTable().refreshColumn();
}
function handleSearchRowClick(record, index, event) {
  record.fundSource = userStore.dictItems.fund_source[0].value;
  record.fundSource_dictText = userStore.dictItems.fund_source[0].text;
  record.index = tableRef.value!.getTableData().length -1
  // 型号处理
  if (record.goodsSpecsDetailOperationType === 'options') {
    record.goodsSpecsDetailOptions.forEach((v, i, arr) => {
      arr[i] = {
        label: v,
        value: v
      };
    })
  }
  // 定数包规格处理
  if (record.quantitativePackageFlag == 1) {
    record.packageList = record.quantitativePackageList.map((v) => {
      return {
        label: v.quantitativePackageSpecs,
        value: v.id,
        quantitativePackageNum: v.quantitativePackageNum,
      }
    })
    record.quantitativePackageId = record.packageList[0]?.value
    record.quantitativePackageNum = record.packageList[0]?.quantitativePackageNum //数量赋值
  } 
  addRow(record);
}
const queryData = useDebounceFn(queryDataByparams, 300);
async function queryDataByparams(value, page) {
  page.goodsName__goodsSimpleCode__goodsCommonName__goodsCode__goodsSpecs_orFlag = value ? `*${value}*` : undefined
  page.pageSize = 10;
  page.pageNo = 1;
  const result = await VoByGoodsCommon(page);
  searchData.value = result.records;
  total.value = result.total;
  searchFlg.value = true;
}
const handaleChangeInp = (props, rows) => {
  if (!page.storageId) { return createMessage.error("请选择响应仓库"); }
  let value
  switch (props.column.key) {
    case 'goodsCode': value = rows.goodsCode
      break;
    case 'goodsName': value = rows.goodsName
      break;
    case 'goodsSpecs': value = rows.goodsSpecs
      break;
    default:
      break;
  }
  if (!value) return searchFlg.value = false
  queryData(value, page)
}
const handleCell = (event)=>{
  searchFlg.value = false;
  pageY.value = event.$event.pageY;
}
const handleInp = (e, record) => {
  record.purchaseNum = setInpLimit(record.purchaseNum, 3);
  record.totalAmt = (record.purchaseNum * record.goodsPrice).toFixed(2)
  record.packageNum = record.purchaseNum / parseInt(record.quantitativePackageNum)
}
//倍数检测
const handleBlur = (rows, event) => {
  if(!rows.purchaseNum) return createMessage.error(`请输入采购数量`);
  if (parseFloat(rows.purchaseNum) % parseInt(rows.quantitativePackageNum) !== 0) {
    rows.packageNum = Math.ceil(Number(rows.purchaseNum) / Number(rows.quantitativePackageNum));
    rows.purchaseNum = Number(rows.packageNum) * Number(rows.quantitativePackageNum) ;
    rows.totalAmt = (Number(rows.purchaseNum) * Number(rows.goodsPrice)).toFixed(2) ;
  }
}
// 定数包规格选择数量处理
const handleSelect = (record, $event) => {
  record.quantitativePackageNum = record.quantitativePackageList.find(v => v.id == $event)?.quantitativePackageNum
  record.purchaseNum = ''
  record.packageNum = ''
}
async function handleDel(props) {
  tableRef.value?.removeRows([props.row])
}
const handleRemarkDepart = (type,props) => { 
  //批量
  if(type === 'more'){
    if (!tableRef.value!.getSelectionData().length) return createMessage.warning("请选择需要备注的物资");
    openAddDepartRemarkModals(true, {
      record:tableRef.value!.getSelectionData(),
      type
    })
  }else{
    // 单个
    const index = props.row.index;
    openAddDepartRemarkModals(true, {
      record:props.row,
      index,
      type
    })
  }
}
const sendRes = (res) => {
  const { formData, type } = res;
  const { departInfoList, departRemarkStr } = formData
  if (type === 'one') {    
    const index = formData.index;
    tableRef.value!.getXTable().getTableData().fullData[index]['departRemark'] = departInfoList;
    tableRef.value!.getXTable().getTableData().fullData[index]['departRemarkStr'] = departRemarkStr;
  } else if (type === 'more'){
    let data = tableRef.value!.getXTable().getTableData().fullData.filter(v=> v.goodsId !== '-1')
    let selectData =  tableRef.value!.getSelectionData().filter(v=> v.goodsId !== '-1')
    data.forEach((v, i) => {
      const rowIndex = v.index; 
      const row = selectData.find(item => item.index === rowIndex);
      if (row) {
        tableRef.value!.getXTable().getTableData().fullData[rowIndex]['departRemark'] = departInfoList;
        tableRef.value!.getXTable().getTableData().fullData[rowIndex]['departRemarkStr'] = departRemarkStr;
      }
    });
  }
  tableRef.value!.getXTable().refreshColumn();
}
const getFoundsInfo = (data) => {
  const tableData = tableRef.value!.getXTable().getTableData().fullData;
  data.forEach(val => {    
    const rowIndex = val.index;
    const row = tableData.find(item => item.index === rowIndex);
    if (row) {
      row.fundSource = val.fundSource;
      row.fundSource_dictText = val.fundSource_dictText;
    }
  });
  tableRef.value?.getXTable().reloadData(tableData);
};
// 生成采购订单预览
const handleOrder = async () => {
  try {
    formRef.value
      .validate()
      .then(async () => {
        innerData.value = tableRef.value!.getXTable().getTableData().fullData //源数据
        const len = innerData.value.length
        if (len == 1) return createMessage.error("请至少添加一条物资");
        createOrderLoading.value = true
        let saveData = innerData.value.filter(v => v.goodsId !== '-1').map(item => {
          return {
            "brand": item.brand,
            "createBy": item.createBy,
            "createTime": item.createTime,
            "delFlag": item.delFlag,
            "fundSource": item.fundSource,
            "departRemark": item.departRemark?.map(v => {
              return {
                "departInfo": {
                  "departId": v.departInfo.value,
                  "departName": v.departInfo.label
                },
                'goodsRemark':v.goodsRemark,
                'num': v.num
              }
            }),
            "departRemarkStr": item.departRemarkStr,
            "goodsCode": item.goodsCode,
            "goodsId": item.goodsId,
            "goodsName": item.goodsName,
            "goodsPrice": item.goodsPrice,
            "goodsSpecs": item.goodsSpecs,
            "goodsSpecsDetail": item.goodsSpecsDetail,
            "goodsType": item.goodsType,
            "id": item.id,
            "manufacturerId": item.manufacturerId,
            "manufacturerName": item.manufacturerName,
            "packageNum": item.purchaseNum / parseInt(item.quantitativePackageNum),
            "purchaseNum": item.purchaseNum,
            "quantitativePackageFlag": item.quantitativePackageFlag,
            "quantitativePackageId": item.quantitativePackageId,
            "quantitativePackageNum": item.quantitativePackageNum,
            "remark": item.remark,
            "supplierId": item.supplierId,
            "supplierName": item.supplierName,
            "sysOrgCode": item.sysOrgCode,
            "tempPurchaseFlag": item.tempPurchaseFlag,
            "tempPurchaseTime": item.tempPurchaseTime,
            "totalAmt": item.totalAmt,
            "unitName": item.unitName,
            "updateBy": item.updateBy,
            "updateTime": item.updateTime,
          }
        });
        let params = {
          previewVO: {
            detailList: saveData,
            documentInfo: {
              ...orderInfo.value,
              storageId: page.storageId
            },
          },
          purchaseFlag: unref(purchaseFlag), //  1 科室采购 2 库房直接采购
        };
        await addPlan(params);
        const r = await purchasePreview({ createFlag: true, purchaseFlag: unref(purchaseFlag), purchasePlanIds: [orderInfo.value?.documentId], pageNo: 1, pageSize: 1000 })
        createOrderLoading.value = false
        orderList.value = r.orderPreviewList
        ids.value = r.orderPreviewList.map(v => v.orderId)
        setModalProps({ title: "采购订单预览", showOkBtn: false });
      })
      .catch((error) => {
        createOrderLoading.value = false
      });
  } catch (e) {
    console.log(e);
  }
}
// 保存推送订单订单
const handleSavePush = async (type) => {
  try {
    // 保存订单
    await add({ planIds: [orderInfo.value?.documentId], preview: { orderPreviewList: orderList.value }, purchaseFlag: unref(purchaseFlag) },)
    if (type === 'save') {
      saveLoading.value = true;
      //跳转到订单页面
    } else if (type === 'push') {
      pushLoading.value = true;
      // 推送订单
      await pushOrder({ ids: ids.value })
    }
    closeModal()
    router.push({ path: '/purchase/purchOrderManage' });
  } finally {
    saveLoading.value = false;
    pushLoading.value = false;
  }
}
//单据汇总
const handleSumOrder = async () => {
  innerData.value = tableRef.value!.getXTable().getTableData().fullData //源数据
  const len = innerData.value.length
  if (len == 1) return createMessage.error("当前汇总数据为空");
  //处理后的数据
  let saveData = innerData.value.filter(v => v.goodsId !== '-1').map(item => {
    return {
      "brand": item.brand,
      "createBy": item.createBy,
      "createTime": item.createTime,
      "delFlag": item.delFlag,
      "fundSource": item.fundSource,
      "departRemark": item.departRemark?.map(v => {
        return {
          "departInfo": {
            "departId": v.departInfo.value,
            "departName": v.departInfo.label
          },
          'goodsRemark':v.goodsRemark,
          'num': v.num
        }
      }),
      "departRemarkStr": item.departRemarkStr,
      "goodsCode": item.goodsCode,
      "goodsId": item.goodsId,
      "goodsName": item.goodsName,
      "goodsPrice": item.goodsPrice,
      "goodsSpecs": item.goodsSpecs,
      "goodsSpecsDetail": item.goodsSpecsDetail,
      "goodsType": item.goodsType,
      "id": item.id,
      "manufacturerId": item.manufacturerId,
      "manufacturerName": item.manufacturerName,
      "packageNum": item.purchaseNum / parseInt(item.quantitativePackageNum),
      "purchaseNum": item.purchaseNum,
      "quantitativePackageFlag": item.quantitativePackageFlag,
      "quantitativePackageId": item.quantitativePackageId,
      "quantitativePackageNum": item.quantitativePackageNum,
      "remark": item.remark,
      "supplierId": item.supplierId,
      "supplierName": item.supplierName,
      "sysOrgCode": item.sysOrgCode,
      "tempPurchaseFlag": item.tempPurchaseFlag,
      "tempPurchaseTime": item.tempPurchaseTime,
      "totalAmt": item.totalAmt,
      "unitName": item.unitName,
      "updateBy": item.updateBy,
      "updateTime": item.updateTime,
    }
  });
  const res = await collect(saveData)
  innerData.value = res
  innerData.value.forEach((v, i) => {
    v.index = i
    if (Array.isArray(v.quantitativePackageList) === true) {
      v.packageList = v.quantitativePackageList.map(item => {
        return {
          label: item.quantitativePackageSpecs,
          value: item.id,
          quantitativePackageNum: item.quantitativePackageNum,
        }
      })
    }
    v.departRemark = v.departRemark?.map(val => {
      return {
        "departInfo": {
          "value": val.departInfo.departId,
          "label": val.departInfo.departName
        },
        'goodsRemark':val.goodsRemark,
        'num': val.num
      }
    })
  })
  innerData.value.push({ goodsId: '-1',index:innerData.value.length })
  tableRef.value?.getXTable().reloadData(innerData.value);
  tableRef.value!.getXTable().refreshColumn();
}
// Excel导入
const handleExcel = async () => {
  formRef.value.validate().then(async () => {
    openExportModal(true,{
      storageId:page.storageId
    })})
  }
const handleAction = async (type) => {
  try{
    formRef.value
    .validate()
    .then( async () => {
      setModalProps( { loading:true } )
      const res = await fillInventory({ storageId: page.storageId, type })
      innerData.value = res
      innerData.value.forEach((v, i) => {
        v.index = i
        if (Array.isArray(v.quantitativePackageList) === true) {
          v.packageList = v.quantitativePackageList.map(item => {
            return {
              label: item.quantitativePackageSpecs,
              value: item.id,
              quantitativePackageNum: item.quantitativePackageNum,
            }
          })
        }
        v.departRemark = v.departRemark?.map(val => {
          return {
            "departInfo": {
              "value": val.departInfo.departId,
              "label": val.departInfo.departName
            },
            'goodsRemark':val.goodsRemark,
            'num': val.num
          }
        })
      })
      innerData.value.push({ goodsId: '-1',index:innerData.value.length })
      tableRef.value?.getXTable().reloadData(innerData.value);
      tableRef.value!.getXTable().refreshColumn();
      setModalProps( { loading:false } )
    });
  }catch(e){
    console.log(e,'e');
  }
  finally{
    setModalProps( { loading:false } )
  }
}
const uploadRes = (res) => {
  orderInfo.value = res?.documentInfo
  orderInfo.value.documentNo = res.documentInfo.documentNo
  orderInfo.value.documentId = res.documentInfo.documentId
  orderInfo.value.createUserRealname = res.documentInfo.createUserRealname
  orderInfo.value.createTime = res.documentInfo.createTime
  page.storageId = res.documentInfo.storageId
  innerData.value = res?.detailList
  innerData.value?.forEach((v, i) => {
    v.index = i
    if (Array.isArray(v.quantitativePackageList) === true) {
      v.packageList = v.quantitativePackageList.map(item => {
        return {
          label: item.quantitativePackageSpecs,
          value: item.id,
          quantitativePackageNum: item.quantitativePackageNum,
        }
      })
    }
    v.departRemark = v.departRemark?.map(val => {
      return {
        "departInfo": {
          "value": val.departInfo.departId,
          "label": val.departInfo.departName
        },
        'goodsRemark':val.goodsRemark,
        'num': val.num
      }
    })
  })
  innerData.value.push({ goodsId: '-1',index:innerData.value.length })
  tableRef.value?.getXTable().reloadData(innerData.value);
  tableRef.value!.getXTable().refreshColumn();
}
const handleSubmit = () => {
  formRef.value
    .validate()
    .then(async () => {
      innerData.value = tableRef.value!.getXTable().getTableData().fullData //源数据
      const len = innerData.value.length
      if (len == 1) return createMessage.error("请至少添加一条物资");
      let saveData = tableRef.value!.getXTable().getTableData().fullData.filter(v => v.goodsId !== '-1').map(item => {
        return {
          "brand": item.brand,
          "createBy": item.createBy,
          "createTime": item.createTime,
          "delFlag": item.delFlag,
          "fundSource": item.fundSource,
          "departRemark": item.departRemark?.map(v => {
            return {
              "departInfo": {
                "departId": v.departInfo.value,
                "departName": v.departInfo.label
              },
              'goodsRemark':v.goodsRemark,
              'num': v.num
            }
          }),
          "departRemarkStr": item.departRemarkStr,
          "goodsCode": item.goodsCode,
          "goodsId": item.goodsId,
          "goodsName": item.goodsName,
          "goodsPrice": item.goodsPrice,
          "goodsSpecs": item.goodsSpecs,
          "goodsSpecsDetail": item.goodsSpecsDetail,
          "goodsType": item.goodsType,
          "id": item.id,
          "manufacturerId": item.manufacturerId,
          "manufacturerName": item.manufacturerName,
          "packageNum": item.purchaseNum / parseInt(item.quantitativePackageNum),
          "purchaseNum": item.purchaseNum,
          "quantitativePackageFlag": item.quantitativePackageFlag,
          "quantitativePackageId": item.quantitativePackageId,
          "quantitativePackageNum": item.quantitativePackageNum,
          "remark": item.remark,
          "supplierId": item.supplierId,
          "supplierName": item.supplierName,
          "sysOrgCode": item.sysOrgCode,
          "tempPurchaseFlag": item.tempPurchaseFlag,
          "tempPurchaseTime": item.tempPurchaseTime,
          "totalAmt": item.totalAmt,
          "unitName": item.unitName,
          "updateBy": item.updateBy,
          "updateTime": item.updateTime,
        }
      })
      setModalProps({ confirmLoading: true })
      let params = {
        previewVO: {
          detailList: saveData,
          documentInfo: {
            ...orderInfo.value,
            storageId: page.storageId
          },
        },
        purchaseFlag: unref(purchaseFlag), //  1 科室采购 2 库房直接采购
      };
      if (formState.isEdit) { //编辑
        await editPlan(params);
      } else if (!formState.isEdit) {  // 新增
        await addPlan(params);
      } else if (isJump.value === '1') {
        await addPlan(params);
      }
      closeModal();
      emit("success");
    })
    .catch((error) => {
    }).finally(() => {
    setModalProps({ confirmLoading: false })
  })
};
onMounted(() => {
  window.addEventListener("click", handleMousedown); //监听鼠标按下
});

function handleMousedown(event) {
  if (event.target.parentNode?.className != "scrollbar__view") return;
  page.pageNo = 1;
  page.pageSize = 10;
  searchFlg.value = false;
}
// 订单列表
const expandedRowKeys = ref([]);
const expandFun = async (expanded, record) => {
  // 只展开一行
  if (expandedRowKeys.value.length > 0) { //进这个判断说明当前已经有展开的了
    //返回某个指定的字符串值在字符串中首次出现的位置，下标为0
    let index = expandedRowKeys.value.indexOf(record.orderId);
    if (index > -1) { //如果出现则截取这个id,1d到1相当于0，针对重复点击一个
      expandedRowKeys.value.splice(index, 1);
    } else {
      //如果没出现则截取所有id,添加点击id，0到1，针对已经有一个展开，点另一个会进入判断
      expandedRowKeys.value.splice(0, expandedRowKeys.value.length);
      expandedRowKeys.value.push(record.orderId);
    }
  } else {
    //数组长度小于0，说明都没展开，第一次点击，id添加到数组，数组有谁的id谁就展开
    expandedRowKeys.value.push(record.orderId);
  }
}
</script>

<style lang="scss" scoped>
.ml10 {
  margin-left: 10px;
}

.check-table {
  position: absolute;
  width: 98%;
  height: 50%;
  background: #fff;
  box-shadow: 1px 1px 6px 0px black;
  z-index: 9999;
}

.boxahadow {
  display: block;
  box-shadow: 1px 1px 6px 0px black;
}

.boxnoshow {
  display: none;
  // box-shadow: 1px 1px 6px 0px black;
}

.remark {
  position: relative;

  &::after {
    position: absolute;
    left: -8px;
    content: '*';
    color: red;
    clear: both;
  }
}
.btns{
  margin-bottom: 20px;
}
</style>
