<template>
  <div class="box">
    <h1 class="h1">欢迎使用华兴SPD物流系统</h1>
  </div>
  <div class="expire-warning">
    <div class="header">效期预警</div>
    <div style="margin-top: 20px;padding-bottom: 50px;">
      <div style=" width: 96%;display: flex;justify-content: space-evenly;margin-bottom: 20px;">
        <div v-for="item in column" class="w16" :style="{ visibility: (item.title === '1' ? 'hidden' : 'visible') }">
          {{ item.title }}
        </div>
      </div>
      <div style="width: 96%;display: flex;justify-content: space-evenly;margin-bottom: 20px;">
        <div class="w16">已过期物资</div>
        <div @click.prevent="jump(0)" class="w16 red">{{ expireData.categoryNum }}</div>
        <div @click.prevent="jump(0)" class="w16 red">{{ expireData.totalNum }}</div>
        <div @click.prevent="jump(0)" class="w16 red">{{ expireData.totalAmt }}</div>
        <div @click.prevent="jump(0)" class="w16 red">{{ expireData.highNum }}</div>
        <div @click.prevent="jump(0)" class="w16 red">{{ expireData.lowNum }}</div>
        <div @click.prevent="jump(0)" class="w16 red">{{ expireData.reagentNum }}</div>
      </div>
      <div style=" width: 96%;display: flex;justify-content: space-evenly;">
        <div class="w16">3个月近效期物资</div>
        <div @click.prevent="jump(1)" class="w16 yellow">{{ effectData.categoryNum }}</div>
        <div @click.prevent="jump(1)" class="w16 yellow">{{ effectData.totalNum }}</div>
        <div @click.prevent="jump(1)" class="w16 yellow">{{ effectData.totalAmt }}</div>
        <div @click.prevent="jump(1)" class="w16 yellow">{{ effectData.highNum }}</div>
        <div @click.prevent="jump(1)" class="w16 yellow">{{ effectData.lowNum }}</div>
        <div @click.prevent="jump(1)" class="w16 yellow">{{ effectData.reagentNum }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { indexTerm } from './api'
import { ref } from 'vue';
import { useUserStore } from "/@/store/modules/user";
const userStore: any = useUserStore();
let storage_id = ref(userStore.hospitalZoneInfo?.storage?.id)

const column = [
  { title: '1', },
  { title: '物资类别', },
  { title: '物资数量', },
  { title: '总金额', },
  { title: '高值物资数量', },
  { title: '低值物资数量', },
  { title: '试剂物资数量', },
]
const expireData = ref({
  indexFlag: '',
  categoryNum: '',
  totalNum: '',
  totalAmt: '',
  highNum: '',
  lowNum: '',
  reagentNum: '',
})//过期
const effectData = ref({
  indexFlag: '',
  categoryNum: '',
  totalNum: '',
  totalAmt: '',
  highNum: '',
  lowNum: '',
  reagentNum: '',

})//有效期

const getList = async () => {
  const res = await indexTerm();
  expireData.value = res?.result[0]
  effectData.value = res?.result[1]
}
getList()
const router = useRouter();
const routes = router.getRoutes();
const jump = (type) => {
  const arr = routes.filter(item => item.path === '/fineReport_spd_31');
  if (arr.length > 0 && arr[0].meta && arr[0].meta.frameSrc) {
    arr[0].meta.frameSrc = arr[0].meta.frameSrc.replace(/([&]term=[^&]*)/, '');
    arr[0].meta.frameSrc += type === 0 ? `&storage_id=${storage_id.value}&term=0` : `&storage_id=${storage_id.value}&term=1`;
  }
  router.push('/fineReport_spd_31');
}
</script>
<style scoped lang="scss">
.w16 {
  width: 16%;
  text-align: center;
  color: #2f4084;
}

.red {
  background-color: #fdacab;
  color: #2f4084;
  text-decoration: underline;
  padding: 6px 0;
}

.yellow {
  background-color: #fdebab;
  color: #2f4084;
  text-decoration: underline;
  padding: 4px 0;
}

.box {
  width: 100%;
  height: 70%;
  background-color: #E4EAFC;
  background-image: url(../../../assets/images/indexPG.png);
  background-size: 30%;
  background-repeat: no-repeat;
  background-position: center center;
  position: relative;

  .h1 {
    font-size: 30px;
    font-weight: 700;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 4%;
    color: #1b2d77;
  }
}

.header {
  display: flex;
  width: 16%;
  background-color: #e7ebfc;
  padding: 20px 0 20px 100px;
  color: #2f4084;
  font-size: 16px;
}

.expire-warning {
  width: 98%;
  margin: auto;
  font-weight: bold;
  border: 2px solid #ccd2fe;
  border-radius: 8px 8px 0 0;
  color: #333;
  border-bottom: 2px solid #d4d7f5;
  transform: translate(0, -70px);
}
</style>
