<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" class="ant-table-striped" :rowClassName="rowClassName" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-if="hasPermission('directOutTransfer:check')" @click="handleExam">复核</a-button>
        <a-button type="primary" v-if="hasPermission('directOutTransfer:revExam')" @click="handleRevExam">反复核</a-button>
        <a-button type="primary"  @click="batchPrintOrders">批量打印出库单</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #form-applyUser="{ model, field }">
        <ApiSelect :api="getSearchUserList" v-model:value="model[field]" allowClear showSearch placeholder="请选择出库人"
          optionFilterProp="label" labelField="realname" valueField="username" resultField="result"
          :params="searchParams" @search="onSearch" @click="onClick" :filterOption="false">
        </ApiSelect>
      </template>
      <template #form-acceptUserName="{ model, field }">
        <ApiSelect :api="getSearchUserList" v-model:value="model[field]" allowClear showSearch placeholder="请选择入库人"
          optionFilterProp="label" labelField="realname" valueField="username" resultField="result"
          :params="searchParams" @search="onSearch" @click="onClick" :filterOption="false">
        </ApiSelect>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="dpflex">
            <div class="ml100">{{ `${checkSumAmount}` }}</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </div>
    </template>
    </BasicTable>
    <!-- 表单区域 -->
    <SpdTransferDetailDrawer @register="registerDrawer"></SpdTransferDetailDrawer>
    <PrintModal v-bind="$attrs" @register="PrinterModal"></PrintModal>
  </div>
  <!-- 打印出库单 -->
  <OuterPrint :list="printList" ref="outerPrintRef"></OuterPrint>
</template>

<script lang="ts" name="storeInout-directOutTransfer" setup>
import { computed, ref, unref } from 'vue'
import { BasicTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { columns, searchFormSchema } from './SpdTransfer.data';
import { list, queryTransferPrint, cancelTransfer, addTransferPrintTimes, review,queryTransferPrints} from './SpdTransfer.api';
import { useDrawer } from "/@/components/Drawer";
import SpdTransferDetailDrawer from "./components/SpdTransferDetailDrawer.vue";
import { useModal } from '/@/components/Modal';
import OuterPrint from '/@/components/Print/SelfGoodsPrint/outerPrint.vue';
import { ApiSelect } from '/@/components/Form/index';
import { useApiSelect } from '/@/spdHooks/useApiSelect'
import { usePermission } from '/@/hooks/web/usePermission';
import { useMessage } from '/@/hooks/web/useMessage';
import { getSearchUserList } from '/@/api/common/api';
const { onSearch, onClick, searchParams } = useApiSelect()
const { hasPermission } = usePermission();
const { createConfirm, createMessage } = useMessage();
import { handleSummaryNew, checkedSum } from '/@/utils/index'
const getFootTotal = (currentPageData)=> handleSummaryNew(currentPageData,'totalAmt','transferNum')
const checkSumAmount = computed(() => checkedSum(selectedRows.value,'totalAmt','transferNum')) // 勾选合计
const [PrinterModal, { openModal: PrinterOpenModal }] = useModal(); // 打印modal
//注册drawer
const [registerDrawer, { openDrawer }] = useDrawer();
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll: { y: 480 },
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
        ['deliverDate', ['beginDate', 'endDate'], 'YYYY-MM-DD']
      ],
    },
    showIndexColumn: true,
    actionColumn: {
      width: 200,
      fixed: 'right'
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      params.beginDate = params.beginDate == null ? null : params.beginDate + ' 00:00:00';
      params.endDate = params.endDate == null ? null : params.endDate + ' 23:59:59';
    },
  },
})

const rowClassName = computed(() => {
  return (record, index: number) => (record.transferStatus === 5 ? "darkYellow" : "");
});


function handleSuccess() {
  reload()
  clearSelectedRowKeys()
}
const [registerTable, { reload, clearSelectedRowKeys }, { rowSelection, selectedRowKeys, selectedRows } ] = tableContext
/**
 * 详情
*/
function handleDetail(record) {
  openDrawer(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
//复核
const handleExam = () => {
  if (!unref(selectedRowKeys).length) return createMessage.error('请选择需要复核的入库单')
  const isAudit = selectedRows.value.some(v => v.reviewStatus == 1)
  const isExam = selectedRows.value.some(v => v.transferStatus != 3)
  if (isAudit) return createMessage.error('当前入库单存在已复核通过的单据，请重新选择')
  if (isExam) return createMessage.error('当前入库单存在未入库的单据，请重新选择')
  createConfirm({
    iconType: 'warning',
    title: '入库单复核',
    content: '是否复核选择的入库单',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await review({ auditStatus: 1, ids: unref(selectedRowKeys) })
      handleSuccess()
    },
  });
}
const handleRevExam = () => {
  if (!unref(selectedRowKeys).length) return createMessage.error('请选择需要反复核的入库单')
  const isAudit = selectedRows.value.some(v => v.reviewStatus == 0)
  if (isAudit) return createMessage.error('存在待复核的单据,请复核后进行反复核')
  createConfirm({
    iconType: 'warning',
    title: '入库单反复核',
    content: '是否反复核选择的入库单',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await review({ auditStatus: 0, ids: unref(selectedRowKeys) })
      handleSuccess()
    },
  });
}

const batchPrintOrders= async ()=>{
  if (!unref(selectedRowKeys).length) return createMessage.error('请选择需要打印的出库单')
  let params = <any>{
    transferIds: selectedRowKeys.value,//依据单号
    pageSize: 6,// pageSize
  }
  const res = await queryTransferPrints(params)
  printList.value = res
  setTimeout(function () {
    outerPrintRef.value.print();
  }, 100);
  let list:any[]=[]
  selectedRows.value.forEach(v=>{
    list.push(v.transferNo)
  })
  await addTransferPrintTimes({ transferNos: list })
  handleSuccess()
}

const printList = ref<any>([])
const outerPrintRef = ref<any>(null)
const handlePrint = async (record) => {
  let params = <any>{
    transferId: record.id,//依据单号
    pageSize: 6,// pageSize
  }
  const res = await queryTransferPrint(params)
  printList.value = res
  setTimeout(function () {
    outerPrintRef.value.print();
  }, 100);
  await addTransferPrintTimes({ transferNos: [record.transferNo] })
  handleSuccess()
}
const handlelBack = async (record) => {
  await cancelTransfer({
    transferIds: [record.id]
  })
  handleSuccess()
}
/**
   * 操作栏
   */
function getTableAction(record) {
  return [
    {
      label: '详情',
      ifShow: true,
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '撤回',
      auth: 'directOutTransfer_revoke',
      popConfirm: {
        title: '是否撤回？',
        confirm: handlelBack.bind(null, record),
      },
      ifShow: (record.settlementType == 1 && record.transferStatus === 2) || (record.settlementType == 2 && record.transferStatus !== 5)
    },
    {
      label: '打印出库单',
      ifShow: true,
      onClick: handlePrint.bind(null, record),
    },
  ]
}

</script>

<style scoped lang="scss">
:deep(.ant-modal-content) {
  .ant-modal-close-x .jeecg-basic-modal-close {
    color: #fff !important;

    & .anticon:hover {
      color: #ed6f6f;
    }
  }

  .ant-modal-header {
    background-color: #1a8efe !important;

    .ant-modal-title .jeecg-basic-title {
      color: #fff !important;
    }
  }
}

.foot {
  .foot-total {
    text-align: right;
  }
}

:v-deep(.ant-table-body) {
  overflow: hidden;
}
:deep(.ant-col-4) {
  margin-left: 25%;
}
.ant-table-striped :deep(.darkYellow) td {
  color: rgb(241, 54, 54);
}
</style>
