<template>
  <BasicDrawer wrapClassName="drawer-custom" placement="bottom" @register="registerDrawer" destroyOnClose :height="800"
    :title="type == 'accept' ? '配送单耗材验收列表' : ' 配送单耗材详细列表'">
    <div class="header-title dpflex" v-if="type == 'accept'">
      <div class="mr10">
        <span style="padding-right: 20px;">耗材条码:</span>
        <a-input ref="inputRef" v-model:value="uniqueCode" style="width: 220px;" @keyup.enter="addRow" />
      </div>
      <div>
        <span style="padding-right: 20px;">UDI:</span>
        <a-input ref="udiRef" v-model:value="udiCode" style="width: 220px;" @keyup.enter="addUDI" />
      </div>
    </div>
    <div v-if="type == 'accept'"><a-button type="primary" @click="allin">{{ isAllSelected ? '取消全选' : '全选' }}</a-button>
    </div>
    <a-table @expand="expandFun" :expandedRowKeys="expandedRowKeys" :showSizeChanger="false" :scroll="{ x: '100%',y:500 }"
      :columns="type == 'accept' ? columnsD : columnsDS" :data-source="deliveryList" rowKey="id" :pagination="false">
      <template #check="record">
        <a-checkbox :indeterminate="record.record.indeterminate"
          v-if="record.record.recordList.length && type == 'accept'" @change="checkAll(record.record, $event)"
          :checked="record.record.isChecked" />
      </template>
      <template #serialNum="{ record, index }">
        <span style="color:#000">{{ index + 1 }}</span>
      </template>
      <template #currentNum="record">
        <!-- <a-input v-if="type == 'accept' && record.record.individualFlag != 1"
          v-model:value="record.record.currentCheckNum" @input="input(record.record)" />
        <span v-else>{{ record.record.currentCheckNum || 0 }}</span> -->
        <span>{{ record.record.currentCheckNum || 0 }}</span>
      </template>
      <template #packagingIntegrityDefectiveNum="record">
        <!-- <a-input v-if="type == 'accept' && record.record.individualFlag != 1"
          v-model:value="record.record.packagingIntegrityDefectiveNum" @input="input2(record.record)" />
        <span v-else>{{ record.record.packagingIntegrityDefectiveNum || 0 }}</span> -->
        <span> {{ Boolean(+record.record.packagingIntegrityDefectiveNum) ? '不合格' : '合格' }}</span>
      </template>
      <template #labelClarityDefectiveNum="record">
        <!-- <a-input v-if="type == 'accept' && record.record.individualFlag != 1"
          v-model:value="record.record.labelClarityDefectiveNum" @input="input3(record.record)" />
        <span v-else>{{ record.record.labelClarityDefectiveNum || 0 }}</span> -->
        <span>{{ Boolean(+record.record.labelClarityDefectiveNum) ? '不合格' : '合格' }}</span>
      </template>
      <template #foreignMatterDefectiveNum="record">
        <!-- <a-input v-if="type == 'accept' && record.record.individualFlag != 1"
          v-model:value="record.record.foreignMatterDefectiveNum" @input="input4(record.record)" />
        <span v-else>{{ record.record.foreignMatterDefectiveNum || 0 }}</span> -->
        <span>{{ Boolean(+record.record.foreignMatterDefectiveNum) ? '不合格' : '合格' }}</span>
      </template>
      <template #customAction="record">
        <span @click="printAll(record.record)">打印全部</span>
      </template>
      <template #expandedRowRender="{ record }">
        <a-table :showSizeChanger="false" v-if="record.recordList.length && record.quantitativePackageFlag != 1"
          style="width: 100%;" :columns="type == 'accept' ? innerColumnsD : innerColumnsD2S" rowKey="id"
          :data-source="record.recordList" :pagination="false" >
          <template #check="{ record }">
            <!-- <span v-if="type == 'accept'" @click="check(record)">
              {{ record.isChecked ? '取消' : '选择' }}
            </span> -->
            <!-- {{ record.isChecked }} -->
            <a-checkbox v-if="type == 'accept'" @change="check(record, $event)" :checked="record.isChecked" />
          </template>
          <template #serialNum="{ record, index }">
            <span style="color:#000">{{ index + 1 }}</span>
          </template>
          <template #isChecked="{ record }">
            <span :class="record.isChecked ? 'active' : 'noactive'">{{ record.isChecked ? '是' : '否' }}</span>
          </template>
          <template #packagingIntegrityFlag="{ record }">
            <span @click="packagingIntegrityFlag(record)"
              :class="record.packagingIntegrityFlag ? 'active' : 'noactive'">{{
                record.packagingIntegrityFlag ? '合格' : '不合格' }}</span>
          </template>
          <template #labelClarityFlag="{ record }">
            <span @click="labelClarityFlag(record)" :class="record.labelClarityFlag ? 'active' : 'noactive'">{{
              record.labelClarityFlag ? '合格' : '不合格' }}</span>
          </template>
          <template #foreignMatterFlag="{ record }">
            <span @click="foreignMatterFlag(record)" :class="record.foreignMatterFlag ? 'active' : 'noactive'">{{
              record.foreignMatterFlag ? '合格' : '不合格' }}</span>
          </template>
          <template #checkFlag="{ record }">
            <span @click="checkFlag(record)" :class="record.checkFlag ? 'active' : 'noactive'">{{ record.checkFlag ?
              '合格'
              :
              '不合格' }}</span>
          </template>
          <template #batchNo="{ record }">
            <div :class="[]">{{ record.batchNo }}</div>
          </template>
          <template #productDate="{ record }">
            <div :class="[]">{{ record.productDate }}</div>
          </template>
          <template #term="{ record }">
            <div :class="[]">{{ record.term }}</div>
          </template>
          <template #customAction="record">
            <span class="table-operation">
              <span style="margin-left: 10px;" @click="print(record.record)">
                打印条码
              </span>
            </span>
          </template>
        </a-table>
      </template>
    </a-table>
    <div class="wrapper" ref="wrapper">
      <!-- 打印机列表 -->
      <print-modal v-bind="$attrs" @register="PrinterModal" :getContainer="() => wrapper" @success="handleSuccess"></print-modal>
    </div>
    <template #footer>
      <a-button v-if="type == 'accept' ? true : false" @click="handleCancle">取消</a-button>
      <a-button v-if="type == 'accept' ? true : false" type="primary" @click="handleSubmit('ok')">确认</a-button>
      <a-button v-if="type == 'accept' ? true : false" type="primary" @click="handleSubmit('enter')">确认并入库</a-button>
    </template>
  </BasicDrawer>
</template>

<script lang="tsx" setup>
import { ref, defineEmits, watch, unref } from 'vue';
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { AccepDetailsQuery } from '../SpdDelivery.api';
import PrintModal from './PrintModal.vue';
import { useModal } from '/@/components/Modal';
import { message } from 'ant-design-vue'
import { deliveryDetailsAccept } from '../SpdDelivery.api'
import { columnsD, innerColumnsD, columnsDS, innerColumnsD2S } from '../SpdDelivery.data'
import lodash from 'lodash'
import { useRouter } from 'vue-router';
import { is } from '/@/utils/is';
const router = useRouter();
const deliveryList = ref<any>([])
const checkStatus = ref() // 状态
const inputRef = ref<any>(null);
const udiRef = ref<any>(null);
const deliveryOrderNo = ref() //配送单号
const [PrinterModal, { openModal: PrinterOpenModal }] = useModal(); // 打印机选择modal
//表单赋值
const [registerDrawer, { setDrawerProps, closeDrawer, changeOkLoading }] = useDrawerInner(async (data) => {
  setDrawerProps({
    showFooter: data.record.type == 'accept' ? true : false,footerHeight:60
  });
  deliveryOrderNo.value = data.record.deliveryOrderNo
  type.value = data.record.type
  checkStatus.value = data.record.checkStatus
  if (data.record.type == 'accept')
    setTimeout(() => {
      inputRef.value.focus()
    })
  isAllSelected.value = false
  deliveryList.value = []
  params.value.deliveryId = data.record.id
  let obj
  if (type.value == 'check') {
    obj = {
      deliveryId: data.record.id,
    }
  } else {
    obj = {
      deliveryId: data.record.id,
      checkStatus_MultiString: '0,1',
      'recordQueryDTO.checkStatus_MultiString': '0'
    }
  }
  AccepDetailsQuery(obj).then(res => {
    deliveryList.value = res
    deliveryList.value.forEach(item => {
      if (item.recordList.length > 0) {
        item.isChecked = false
        item.indeterminate = false
        item.recordList.forEach(i => {
          i.isChecked = false
          i.packagingIntegrityFlag = true
          i.labelClarityFlag = true
          i.foreignMatterFlag = true
          i.checkFlag = true
          i.pid = item.id
        })
      }
    })
    // console.log(deliveryList.value, 'deliveryList.value');
  })
  uniqueCode.value = ''
});
let params = ref<any>({
  deliveryId: '',
  details: []
})
const input = (record) => {
  console.log(+record.currentCheckNum);

  if (+record.currentCheckNum + +record.checkNum > +record.deliveryNum) {
    message.warning('本次验收数量+已验收数量不能大于送货数量')
    record.currentCheckNum = 0
  }
}
const input2 = (record) => {
  if (+record.packagingIntegrityDefectiveNum > +record.deliveryNum) {
    message.warning('内外包装是否完整不合格数量不能大于送货数量')
    record.packagingIntegrityDefectiveNum = 0
  }
}
const input3 = (record) => {
  if (+record.labelClarityDefectiveNum > +record.deliveryNum) {
    message.warning('是否标签清晰不合格数量不能大于送货数量')
    record.labelClarityDefectiveNum = 0
  }
}
const input4 = (record) => {
  if (+record.foreignMatterDefectiveNum > +record.deliveryNum) {
    message.warning('是否有异物不合格数量不能大于送货数量')
    record.foreignMatterDefectiveNum = 0
  }
}
const wrapper = ref(null)
const uniqueCode = ref('')
const udiCode = ref('')
const setClassName = (record) => 'red'
const type = ref('')
// Emits声明
const emit = defineEmits(['register', 'success']);

/**
  * 选择列配置
  */
const printAll = (record) => {
  PrinterOpenModal(true, {
    record,
    checkStatus: unref(checkStatus),
    isUpdate: false,
    showFooter: false,
  });
}
const print = (record) => {
  record.individualFlag = 1
  PrinterOpenModal(true, {
    record,
    checkStatus: unref(checkStatus),
    isUpdate: false,
    showFooter: false,
  });
}
// 扫码匹配
const addRow = () => {
  let scanSuccess = false;
  let flag = 0; // 标记条件满足

  deliveryList.value.some(item => {
    if (item.recordList) {
      return item.recordList.some(v => {
        if (uniqueCode.value.trim() === v.uniqueCode) {
          if (v.checkUdiFlag === 1) {
            console.log(1);

            scanSuccess = true;
            message.success('耗材条码扫描成功');
            item.currentCheckNum = item.recordList.filter(v1 => v1.isChecked === true && Boolean(v1.checkFlag) === true).length;
            udiCode.value = '';
            udiRef.value.focus();
            return true; // 停止内循环
          } else if (v.checkUdiFlag === 0 || v.checkUdiFlag === null) {
            console.log(0);
            v.isChecked = true;
            flag = 0; // 标记 checkUdiFlag 为0,不需要双码验证
            if (item.quantitativePackageFlag == 0) {
              item.currentCheckNum = item.recordList.filter(v1 => v1.isChecked === true && Boolean(v1.checkFlag) === true).length;
            } else {
              item.currentCheckNum = item.deliveryNum
            }
            return true;
          }
        } else {
          flag = 1; // uniqueCode 不匹配的情况
        }
        return false; // 继续内循环
      });
    }

    return false; // 继续外循环
  });

  if (!scanSuccess) {
    if (flag === 0) {
      message.success('耗材条码扫描成功');
    } else if (flag === 1) {
      message.error('条码不匹配，扫描失败');
    } else {
      message.error('扫描失败');
    }
    uniqueCode.value = '';
  }
}
//格式化日期
const formatDate = (date) => {
  const year = '20' + date.substring(0, 2);
  const month = date.substring(2, 4);
  const day = date.substring(4, 6);
  return `${year}-${month}-${day}`;
}
const addUDI = () => {
  let obj;

  try {
    obj = JSON.parse(udiCode.value)['UI'] ? JSON.parse(udiCode.value)['UI'] : JSON.parse(udiCode.value)['UDI'];
    obj['EXP'] = obj['EXP'] ? formatDate(obj['EXP']) : undefined;
    udiCode.value = obj['HRI'];
    // debugger
    console.log(obj, 'obj');
    
  } catch (e) {
    console.log(e, 'e');
    udiCode.value = '';
    message.error('UDI格式错误.');
    return;
  }

  // 检查是否已扫描耗材条码
  if (!uniqueCode.value) {
    message.error('请先扫描耗材条码');
    return;
  }

  let foundMatch = false; // 标记是否找到匹配项

  deliveryList.value.some(item => {
    if (item.recordList) {
      return item.recordList.some(v => {
        if (udiCode.value === v.udiCode && uniqueCode.value === v.uniqueCode) {
          foundMatch = true; 

          // 效期校验逻辑：如果UDI效期以-00结尾，只校验年月；否则完全匹配
          let expMatched = false; // 标记效期是否匹配
          if (obj['EXP'].endsWith('-00')) {
            // UDI效期日期为00，只校验年月部分
            expMatched = (obj['EXP'].slice(0, -3) === v.term.slice(0, -3));
          } else {
            // 正常情况下完全匹配
            expMatched = (obj['EXP'] === v.term);
          }

          // 如果效期不匹配，检查是否同时批号也不匹配
          if (!expMatched) {
            if (!obj['LOT']) {
              // UDI中没有批号段，但效期也不匹配
              message.error('UDI与产品有效期不一致,请确认.');
            } else if (obj['LOT'] !== v.batchNo) {
              // 效期和批号都不匹配
              message.error('UDI与产品有效期和产品批号不一致,请确认.');
            } else {
              // 只是效期不匹配
              message.error('UDI与产品有效期不一致,请确认.');
            }
            udiCode.value = '';
            return true;
          }

          // 批号效期校验成功
          if (!obj['LOT']) {
            message.warning('当前物资UDI并未提供批号字段，请人工核对实物批号与配送单批号是否一致！');
          } else if(obj['LOT'] !== v.batchNo) {
            message.warning('UDI与产品批号不一致.');
          } else if (!obj['EXP'].endsWith('-00')) {
            // 完全匹配的情况
            message.success('UDI扫描成功.');
          }

          // 特殊情况：UDI效期以-00结尾且年月匹配
          if (obj['EXP'].endsWith('-00') && expMatched) {
            message.success('UDI扫描成功.');
          }

          //匹配成功的处理
          v.isChecked = true;
          item.currentCheckNum = item.recordList.filter(v1 => v1.isChecked === true && Boolean(v1.checkFlag) === true).length;
          uniqueCode.value = '';
          udiCode.value = '';
          inputRef.value.focus();
          return true; 
        }
        return false; 
      });
    }
    return false; 
  });

  // 如果没有找到匹配的 UDI，显示错误消息
  if (!foundMatch) {
    message.error('UDI与物资条码不匹配,请确认.');
    udiCode.value = '';
  }
};



const check = (record, e) => {
  // console.log(e, record, 'record')
  record.isChecked = !record.isChecked
  deliveryList.value.forEach(item => {
    if (item.id == record.pid) {
      item.currentCheckNum = item.recordList.filter(v => v.isChecked == true && Boolean(v.checkFlag) === true).length
    }
  })
}
const packagingIntegrityFlag = (record) => {
  record.packagingIntegrityFlag = !record.packagingIntegrityFlag
  record.isChecked = true
  // 未验收 直接用length 
  // 部分验收累加
  if (checkStatus.value === 0) {
    deliveryList.value.forEach(item => {
      if (item.id == record.pid) {
        item.currentCheckNum = item.recordList.filter(v => v.isChecked == true && Boolean(v.checkFlag) === true).length
        item.packagingIntegrityDefectiveNum = item.recordList.filter(v => v.packagingIntegrityFlag == false).length
      }
    })
  } else if (checkStatus.value === 1) {
    deliveryList.value.forEach(item => {
      if (item.id == record.pid) {
        if (!record.packagingIntegrityFlag) {
          item.packagingIntegrityDefectiveNum += 1
        } else {
          item.packagingIntegrityDefectiveNum += -1
        }
        item.currentCheckNum = item.recordList.filter(v => v.isChecked == true && Boolean(v.checkFlag) === true).length
      }
    })
  }
}
const labelClarityFlag = (record) => {
  record.labelClarityFlag = !record.labelClarityFlag
  record.isChecked = true
  if (checkStatus.value === 0) {
    deliveryList.value.forEach(item => {
      if (item.id == record.pid) {
        item.currentCheckNum = item.recordList.filter(v => v.isChecked == true && Boolean(v.checkFlag) === true).length
        item.labelClarityDefectiveNum = item.recordList.filter(v => v.labelClarityFlag == false).length
      }
    })
  } else if (checkStatus.value === 1) {
    deliveryList.value.forEach(item => {
      if (item.id == record.pid) {
        if (!record.labelClarityFlag) {
          item.labelClarityDefectiveNum += 1
        } else {
          item.labelClarityDefectiveNum += -1
        }
        item.currentCheckNum = item.recordList.filter(v => v.isChecked == true && Boolean(v.checkFlag) === true).length
      }
    })
  }
}
const foreignMatterFlag = (record) => {
  record.foreignMatterFlag = !record.foreignMatterFlag
  record.isChecked = true
  if (checkStatus.value === 0) {
    deliveryList.value.forEach(item => {
      if (item.id == record.pid) {
        item.currentCheckNum = item.recordList.filter(v => v.isChecked == true && Boolean(v.checkFlag) === true).length
        item.foreignMatterDefectiveNum = item.recordList.filter(v => v.foreignMatterFlag == false).length
      }
    })
  } else if (checkStatus.value === 1) {
    deliveryList.value.forEach(item => {
      if (item.id == record.pid) {
        if (!record.foreignMatterFlag) {
          item.foreignMatterDefectiveNum += 1
        } else {
          item.foreignMatterDefectiveNum += -1
        }
        item.currentCheckNum = item.recordList.filter(v => v.isChecked == true && Boolean(v.checkFlag) === true).length
      }
    })
  }
}
const checkFlag = (record) => {
  // console.log(record);

  record.checkFlag = !record.checkFlag
  record.isChecked = true
  deliveryList.value.forEach(item => {
    if (item.id == record.pid) {
      item.currentCheckNum = item.recordList.filter(v => v.checkFlag == true && v.isChecked === true).length
    }
  })
}
const checkAll = (record, e) => {
  // console.log(record, e, 'e');
  record.isChecked = !record.isChecked
  record.recordList.forEach(item => {
    item.isChecked = record.isChecked
  })
  if (record.isChecked) {
    if (record.quantitativePackageFlag == 0) {
      record.currentCheckNum = record.recordList.filter(v => v.checkFlag == true && v.isChecked === true)?.length
    } else {
      record.currentCheckNum = record.quantitativePackageNum
    }
  } else {
    record.currentCheckNum = 0
  }
}
const isAllSelected = ref(false)
watch(
  () => deliveryList,
  (newValue, oldValue) => {
    newValue.value.forEach(item => {
      let filterData = item.recordList.filter(v => v.isChecked == true).length
      item.indeterminate = !!filterData && filterData < item.recordList.length;
      item.isChecked = item.recordList.length == filterData
    })
  },
  { deep: true }
)
const allin = () => {
  isAllSelected.value = !isAllSelected.value
  deliveryList.value.forEach(item => {
    item.isChecked = isAllSelected.value;
    item.recordList.forEach(record => {
      record.isChecked = isAllSelected.value;
    });
    if (item.individualFlag == 0) {
      if (item.quantitativePackageFlag == 1) {
        if (isAllSelected.value) {
          item.currentCheckNum = item.quantitativePackageNum
        } else {
          item.currentCheckNum = 0
        }
      } else {
        item.currentCheckNum = item.deliveryNum
      }
    } else {
      item.currentCheckNum = item.recordList.filter(v => v.isChecked).length
    }
  });
}
const expandedRowKeys = ref([]);
const expandFun = async (expanded, record) => {
  console.log(1);
  // 只展开一行
  if (expandedRowKeys.value.length > 0) { //进这个判断说明当前已经有展开的了
    //返回某个指定的字符串值在字符串中首次出现的位置，下标为0
    let index = expandedRowKeys.value.indexOf(record.id);
    if (index > -1) { //如果出现则截取这个id,1d到1相当于0，针对重复点击一个
      expandedRowKeys.value.splice(index, 1);
    } else {
      //如果没出现则截取所有id,添加点击id，0到1，针对已经有一个展开，点另一个会进入判断
      expandedRowKeys.value.splice(0, expandedRowKeys.value.length);
      expandedRowKeys.value.push(record.id);
    }
  } else {
    //数组长度小于0，说明都没展开，第一次点击，id添加到数组，数组有谁的id谁就展开
    expandedRowKeys.value.push(record.id);
  }
}
//提交事件
const handleSubmit = lodash.debounce(async (type) => {
  console.log(deliveryList.value);

  try {
    params.value.details = deliveryList.value
    let processedArrayObject = JSON.parse(JSON.stringify(params.value.details)).filter(item => item.currentCheckNum != 0)
    let newArr = processedArrayObject.map(item => {
      return {
        checkConclusion: item.checkConclusion,
        currentCheckNum: item.currentCheckNum ? +item.currentCheckNum : 0,
        packagingIntegrityDefectiveNum: item.packagingIntegrityDefectiveNum ? +item.packagingIntegrityDefectiveNum : 0,
        labelClarityDefectiveNum: item.labelClarityDefectiveNum ? +item.labelClarityDefectiveNum : 0,
        foreignMatterDefectiveNum: item.foreignMatterDefectiveNum ? +item.foreignMatterDefectiveNum : 0,
        detailId: item.id,
        settlementType: item.settlementType,
        recordList: item.recordList.filter(v => v.isChecked == true).map(v => {
          return {
            checkConclusion: v.checkConclusion,
            checkFlag: +v.checkFlag,
            foreignMatterFlag: +v.foreignMatterFlag,
            labelClarityFlag: +v.labelClarityFlag,
            packagingIntegrityFlag: +v.packagingIntegrityFlag,
            recordId: v.id,
          }
        })
      }
    })
    let obj = {
      deliveryId: params.value.deliveryId,
      details: newArr
    }
    console.log(obj, 'obj');

    // console.log(obj, 'obj');
    // 配送验收
    changeOkLoading(true)
    await deliveryDetailsAccept(obj);
    if (type === 'ok') {
      //刷新列表
      emit('success');
    } else {
      router.push({ name: 'storeInout-instore', state: { obj: { deliveryOrderNo: unref(deliveryOrderNo), isJump: true } } })
    }
    //关闭弹窗
    closeDrawer();
  }
  catch (e) {
    console.log(e)
  }
  finally {
    changeOkLoading(false)
  }
}, 500)
const handleCancle = () => { closeDrawer() }


const handleSuccess = () => {

  let obj
  if (type.value == 'check') {
    obj = {
      deliveryId: params.value.deliveryId,
    }
  } else {
    obj = {
      deliveryId: params.value.deliveryId,
      checkStatus_MultiString: '0,1',
      'recordQueryDTO.checkStatus_MultiString': '0'
    }
  }
  AccepDetailsQuery(obj).then(res => {
    for (let i = 0; i < res.length; i++) {
      if (res[i].recordList.length > 0) {
        res[i].isChecked=deliveryList.value[i].isChecked
        res[i].indeterminate=deliveryList.value[i].indeterminate
        for (let j = 0; j < res[i].recordList.length; j++) {
          res[i].recordList[j].isChecked=deliveryList.value[i].recordList[j].isChecked
          res[i].recordList[j].packagingIntegrityFlag=deliveryList.value[i].recordList[j].packagingIntegrityFlag
          res[i].recordList[j].labelClarityFlag=deliveryList.value[i].recordList[j].labelClarityFlag
          res[i].recordList[j].foreignMatterFlag=deliveryList.value[i].recordList[j].foreignMatterFlag
          res[i].recordList[j].checkFlag=deliveryList.value[i].recordList[j].checkFlag
          res[i].recordList[j].pid=deliveryList.value[i].recordList[j].pid
        }
      }
      
    }
    deliveryList.value = res
  })

}




</script>

<style lang="scss" scoped>
::v-deep(.ant-table-expanded-row-fixed) {
  width: 100% !important;
}

//隐藏a-table组件的滚动条
// ::v-deep .ant-table-fixed-header>.ant-table-content>.ant-table-scroll>.ant-table-body {
//   overflow: hidden !important;
// }

:deep(.ant-modal-content) {
  .ant-modal-close-x .jeecg-basic-modal-close {
    color: #fff !important;

    & .anticon:hover {
      color: #ed6f6f;
    }
  }

  .ant-modal-header {
    background-color: #1a8efe !important;

    .ant-modal-title .jeecg-basic-title {
      color: #fff !important;
    }
  }
}

span {
  cursor: pointer;
  color: rgb(0, 78, 247);
}

.active {
  color: rgb(0, 78, 247);
}

.noactive {
  color: #000;
}

// table高度
.drawer-custom {
  .ant-drawer-header {
    .ant-drawer-title {
      color: red !important;
    }
  }
}

::v-deep(.red) {
  color: red;
}

// .ant-drawer-body .scrollbar  .scrollbar__wrap .scrollbar__view{
//   height: 100%;
// }
// .scrollbar__wrap .scrollbar__view {
//   .header-title span{
//   color: #f60;
// }
// }
// ::v-deep .jeecg-basic-drawer .ant-drawer-body .scrollbar__wrap{
//   padding: 10px !important;
// }
// ::v-deep .ant-drawer-body .scrollbar__wrap .scroll-container .scrollbar__view{
//   height: 100% !important;
// }
// .scrollbar__wrap .scrollbar__view{
//   height: 100% !important;
// }
// .ant-table-wrapper{
//   height: 100%;
// }
// .ant-spin-nested-loading{
//   height: 100%;
// }</style>
