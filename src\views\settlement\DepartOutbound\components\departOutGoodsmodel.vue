<template>
    <BasicModal v-bind="$attrs" @register="registerModal" @cancel="close" @ok="ok" destroyOnClose :maskClosable="false"
        :defaultFullscreen="true">
        <div style="font-size: 20px;font-weight: 700;">
            <span style="padding-top: 4px; margin-left: 10px">出库仓库：</span>
            <a-select :disabled="true" v-model:value="formData.storageId" placeholder="请选择响应仓库" style="width: 220px"
                allowClear showSearch optionFilterProp="storageNameAbbr" :options="formData.storeList"></a-select>
        </div>
        <BasicTable @register="registerTable">
            <template #tableTitle>
                <a-button type="primary" @click="handleSelect">选择库存</a-button>
                <a-button type="primary" @click="handleNumPackages"  v-auth="'depart-inventory-out'">选择定数包库存</a-button>
                <a-input v-model:value="formData.code" type="like" style="width: 400px;" placeholder="请扫描条码"
                    @keyup.enter="enterSearch">
                    <template #addonBefore>
                        <span>扫描条码：</span>
                    </template>
                </a-input>
            </template>
            <template #currentOutStoreNum="{ record }">
                <a-input @input="input(record)" v-model:value="record.currentOutStoreNum"
                    :disabled="record.quantitativePackageCode||record.individualFlag==1" />
            </template>
            <template #billTotalAmt="{ record }">
                {{ currentAmt(record).toFixed(2) }}
            </template>
            <template #action="{ record }">
                <TableAction :actions="[
                    {
                        label: '删除',
                        onClick: handleDelete.bind(null, record),
                    },
                ]" />
            </template>
             <template #footer>
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="foot-total">{{ getFootTotal }}</div>
      </div>
    </template>
        </BasicTable>
    </BasicModal>
    <InventoryModal @register="InventoryModals" @getSelectRow="setValue"></InventoryModal>
    <NumPackagesModal @register="NumPackagesModals" @getSelectRow="setValue"></NumPackagesModal>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits } from "vue";
import { BasicTable, useTable, TableAction } from "/@/components/Table";
import { getspdStoragelist } from "/@/api/common/api";
import { addByBatchNo, inventoryDetailPageList } from "../index.api";
import { BasicModal, useModal, useModalInner } from "/@/components/Modal";
import { lowPackagesColumns } from "../index.data";
import InventoryModal from "./inventorymodel.vue";
import NumPackagesModal from "./numPackagesmodel.vue";
import { useUserStore } from "/@/store/modules/user";
import { useMessage } from "/@/hooks/web/useMessage";
import { message } from "ant-design-vue";
import { getNumToThousands } from "/@/utils/index";

const getFootTotal = ref('')
const { createMessage } = useMessage();
const emit = defineEmits(["success"]);
const userStore = useUserStore();
//计算当前金额
const currentAmt = (record) =>
    record.currentOutStoreNum * record.goodsPrice
        ? record.currentOutStoreNum * record.goodsPrice
        : 0;
//表单数据
const formData: any = reactive({
    storageId: userStore.hospitalZoneInfo?.storage.id,
    storeList: [],
    code: "",
});
//数据源
const dataSource: any = ref([]);
//注册modal
const [InventoryModals, { openModal: openInventoryModal }] = useModal();
const [NumPackagesModals, { openModal: openNumPackagesModal }] = useModal();


//注册model
const [registerModal, { closeModal, setModalProps }] = useModalInner((_data) => {
    getFootTotal.value =''
    getStoreList();
    setModalProps({
        width: 1400,
        title: "科室库存出库",
        okText: "确认",
    });
});
//注册table
const [registerTable] = useTable({
    clickToRowSelect: false,
    dataSource,
    rowKey: 'id',
    bordered: true,
    size: "small",
    columns: lowPackagesColumns,
    pagination: false,
    ellipsis: true,
    actionColumn: {
        width: 100,
        title: '操作',
        dataIndex: 'action',
        slots: { customRender: 'action' },
        fixed: 'right',
    },
});
//获取仓库列表
const getStoreList = async () => {
    const res = await getspdStoragelist({ column: "storageType", order: "asc", });
    formData.storeList = res;
    formData.storeList.forEach((v) => {
        v.label = v.storageName;
        v.value = v.id;
        v.storageNameAbbr = v.storageName + v.simpleCode;
    });
};

//Input输入框输入事件
const input = (e) => {
    if (Number(e.currentOutStoreNum) > e.currentNum) {
        e.currentOutStoreNum = 0;
        return createMessage.warning("出库数量不能大于当前库存");
    }
    console.log(dataSource.value);
    
    getFootTotal.value = handleSummaryNew(dataSource.value, 'goodsPrice', 'currentOutStoreNum')
};
//搜索按钮事件
const enterSearch = async () => {
    let res = await inventoryDetailPageList({ code: formData.code, storageId: formData.storageId,verificationMethod:1, queryType:5 });
    if (res.length > 0) {
        let packArr = dataSource.value.filter((v) => v.id == res[0].id);
        if (packArr.length) {
            message.warning("该物资已存在，请勿重复添加");
            formData.code = "";
        } else {
            if (res[0].individualFlag == 1) {
               res[0].currentOutStoreNum = 1;
            }
            dataSource.value.push(res[0]);
            formData.code = "";
        }
    } else {
        message.warning("当前条码不存在");
        formData.code = "";
    }
}
//删除按钮事件
const handleDelete = (record) => {
    dataSource.value = dataSource.value.filter((v) => v.id !== record.id);
}
//选择库存按钮事件
const handleSelect = () => {
    openInventoryModal(true, {
        record: dataSource,
        isUpdate: true,
        showFooter: true,
    });
}
//选择定数包按钮事件
const handleNumPackages = () => {
    openNumPackagesModal(true, {
        record: dataSource,
        isUpdate: true,
        showFooter: true,
    });
}

/**
 * 返回勾选数据
 */

function setValue(options) {
    console.log(options);
    console.log(dataSource.value);
    
    if (options.quantitativePackageFlag == 1) {
        dataSource.value = dataSource.value.filter((v) => v.quantitativePackageFlag !== 1);
        dataSource.value.push(...options.data)
    } else if (options.quantitativePackageFlag == 0) {
        dataSource.value = dataSource.value.filter((v) => v.quantitativePackageFlag !== 0);
        dataSource.value.push(...options.data)
    }
    getFootTotal.value = handleSummaryNew(dataSource.value, 'goodsPrice', 'currentOutStoreNum')
}
/**
 * 获取合计
 */
function handleSummaryNew (tableData,totalAmt,applyOrderNum) {
  // 金额合计
  const totalAmount = tableData.reduce((prev, next) => {
    prev += Number(next[totalAmt]*next[applyOrderNum]?next[totalAmt]*next[applyOrderNum] : 0);
    return prev;
  }, 0);
  // 数量总计
  const totalNumber = tableData.reduce((prev, next) => {
    prev += Number(next[applyOrderNum]?next[applyOrderNum] : 0);
    return prev;
  }, 0);
  return `本页总数量 : ${totalNumber.toFixed(2)} ,\xa0`+`本页总金额 : ${getNumToThousands(totalAmount)}`;
}
//取消按钮事件
const close = () => {
    dataSource.value = [];
    closeModal();
};
//确认按钮事件
const ok = async () => {
    if (dataSource.value.length == 0) {
        return createMessage.warning("请添加库存");
    }
    let params = dataSource.value.map((item) => {
        return {
            batchNo: item.batchNo,
            currentOutStoreNum: item.currentOutStoreNum,
            inventoryId: item.quantitativePackageFlag == 1 ? item.id : item.inventoryId,
            quantitativePackageFlag: item.quantitativePackageFlag,
            term: item.term,
            isConsignmentOutStoretype: 1,
            barcodeType: item.barcodeType,
            goodsCode: item.goodsCode,
        };
    });
    await addByBatchNo(params);
    dataSource.value = [];
    emit("success");
    closeModal();
};
</script>

<style lang="scss" scoped></style>