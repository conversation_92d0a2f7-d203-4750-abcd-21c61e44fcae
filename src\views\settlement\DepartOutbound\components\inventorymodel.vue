<template>
    <BasicModal v-bind="$attrs" @register="registerModal" @cancel="cancel" @ok="ok" destroyOnClose width="1400" 
        :maskClosable="false" :default-fullscreen="true">
        <BasicTable @register="registerTable" :rowSelection="rowSelection">
            <template #currentOutStoreNum="{ record }">
                <a-input @input="input(record)" v-model:value="record.currentOutStoreNum" :disabled="record.individualFlag==1 " />
            </template>
            <template #billTotalAmt="{ record }">
                {{ currentAmt(record).toFixed(2) }}
            </template>
        </BasicTable>
    </BasicModal>
</template>

<script setup lang="ts">
import { ref, defineEmits } from "vue";
import { BasicTable, } from "/@/components/Table";
import { lowlList } from "../index.api";
import { BasicModal, useModalInner } from "/@/components/Modal";
import { lowColumns, lowFormSchema } from "../index.data";
import { useMessage } from "/@/hooks/web/useMessage";
import { useUserStore } from "/@/store/modules/user";
import { useListPage } from "/@/hooks/system/useListPage";


const emit = defineEmits(["getSelectResult", "getSelectRow", "reset"]);
const { createMessage } = useMessage();
const userStore = useUserStore();

const currentAmt = (record) =>
    record.currentOutStoreNum * record.goodsPrice
        ? record.currentOutStoreNum * record.goodsPrice
        : 0;
        //注册tableA
const { tableContext } = useListPage({
    tableProps: {
        api: lowlList,
        columns: lowColumns,
        rowKey: "id",
        bordered: true,
        size: "small",
        canResize: false,
        scroll:{y:500},
        showIndexColumn: true,
        formConfig: {
            labelWidth: 100,
            baseColProps: {
                xs: 24,
                sm: 8,
                md: 6,
                lg: 8,
                xl: 6,
                xxl: 6,
            },
            schemas: lowFormSchema,
            autoSubmitOnEnter: true,
            showAdvancedButton: false,
            fieldMapToNumber: [],
        },
        showActionColumn: false,
        beforeFetch(params) {
            params.storageId = userStore.hospitalZoneInfo?.storage.id
            params.quantitativePackageFlag=0
        },
        afterFetch(data) { 
           data.forEach((item) => {
                if (item.individualFlag == 1){
                    item.currentOutStoreNum = 1;
                }
            });
        },
    },
});
const [registerTable, { setSelectedRowKeys,getSelectRows,getRowSelection }, { rowSelection, selectedRows }] = tableContext
//注册model
let keysArr: any = []
let rowsArr: any = []
const [registerModal, { setModalProps, closeModal }] = useModalInner((data) => {
    keysArr=[]
    let option = [...data.record]
    option.forEach((item) => {
        item=JSON.parse(JSON.stringify(item))
        if (item.quantitativePackageFlag == 0) {
            keysArr.push(item.id)
            rowsArr.push(item)
        }
    });
    setSelectedRowKeys(keysArr)
    selectedRows.value=rowsArr
    console.log(selectedRows.value);
    setModalProps({
        width: 1400,
        title: "科室库存",
        okText: "确认",
        height: 600,
    });

});






const input = (e) => {
    if (Number(e.currentOutStoreNum) > e.currentNum) {
        e.currentOutStoreNum = 0;
        return createMessage.warning("出库数量不能大于当前库存");
    }
};




const ok = async () => {
    console.log(selectedRows.value);
    emit("getSelectRow", { data: selectedRows.value, quantitativePackageFlag: 0 });
    emit("reset");
    //关闭弹窗
    closeModal();
};
</script>

<style lang="scss" scoped></style>