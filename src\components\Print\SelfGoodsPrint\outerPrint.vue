<template>
  <div>
    <iframe
      style="display: none"
      ref="iframeRef"
      :srcdoc="outerContainer"
      frameborder="0"
    ></iframe>
    <div id="print" style="display: none">
      <div
        class="print-outer"
        v-for="(item, index) in props.list"
        :key="item.id"
        style="page-break-after: always"
      >
        <div style="text-align: center;font-size:14px !important;font-weight: 600;">
          首都医科大学附属北京{{ title }}物资出库单
        </div>
        <div class="mt-4" style="text-align: left; width: 100%; display: flex">
          <div style="width: 25%;font-weight: 600;">出库单号: {{ item.transferNo }}</div>
          <span style="width: 16%;margin-left: 10px !important;">出库日期: {{ item.deliverDate }}</span>
          <span style="width: 20%;margin-left: 10px !important;">出库仓库: {{ item.sourceStorageName }}</span>
        </div>
        <div class="mt-4" style="text-align: left; width: 100%; display: flex">
          <span style="width: 25%;">收货地址: {{ item.acceptAddress }}</span>
          <span style="width: 16%;margin-left: 10px !important;">申领科室: {{ item.applyDepartName }}</span>
          <span style="width: 20%;margin-left: 10px !important;">申领人: {{ item.applyUser }}</span>
        </div>
        <div class="mt-4" style="text-align: left; width: 100%;display: flex;">
          <div style="width: 44%;">核算类型: {{ item.fundSource }}</div>
          <div style="width: 56%;white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">摘要: {{ item.remark }}</div>
        </div>
        <div class="imgContainers" style="position: absolute; right: 0px; top: 20px">
          <barcode-print
            :key="item + new Date()"
            :value="item.transferNo"
            :height="35"
            :width="0.9"
            :displayValue="true"
          >
          </barcode-print>
        </div>

        <!-- <div style="border: 0.5px solid #474747;width: 100%;"></div> -->
        <a-table
          style="height: 280px"
          :dataSource="item.detailList"
          :columns="columns"
          :pagination="false"
          :rowKey="(record)=>record.transferNo"
        >
          <template #goodsSpecsDetail="{ record }">
            <span v-if="getStrLen(record) <= 30" class="td" style="color:#000">{{ getSpecs(record) }}</span>
            <span v-else class="td clamp4" :style="{color:'#000',fontSize:'8px !important'}">{{ getSpecs(record) }}</span>
          </template>
        </a-table>
        <!-- <div v-if="item.totalPage == item.currentPage" style="border: 0.5px solid #474747;width: 100%;"></div> -->
        <div style="border: 0.5px solid #474747;width: 100%;"></div>
        <div class="mt-6" style="display: flex;">
          <span style="width: 70%;">本页合计</span>
          <span style="width: 15%;">数量: {{ item.currentPageTotalNum }}</span>
          <span style="width: 15%;">金额: {{ item.currentPageTotalAmt }}</span>
        </div>
        <!-- <div v-if="item.totalPage == item.currentPage" class="mt-6" style="display: flex;"> -->
        <div class="mt-6" style="display: flex;">
          <span style="width: 70%;">本单合计</span>
          <span style="width: 15%;">数量: {{ item.allPageTotalNum }}</span>
          <span style="width: 15%;">金额: {{ item.allPageTotalAmt }}</span>
        </div>
        <div style="border: 0.5px solid #474747; width: 100%"></div>
        <div class="mt-4" style="display: flex">
          <span style="width: 47%">库管员:{{ userStore.getUserInfo?.realname }}</span>
          <span style="width: 32%">科室收货人:</span>
          <span style="width: 20%">审核:</span>
        </div>
        <div class="mt-6" style="text-align: center">
          <span>—— {{ item.currentPage }}/{{ item.totalPage }} ——</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, defineProps } from "vue";
import { useUserStore } from "/@/store/modules/user";
import { getSpecs, printStyle, getStrLen } from '/@/utils/index';
import QRCode from "qrcodejs2-fix";
const userStore = useUserStore();
let title = ref(userStore.hospitalZoneInfo.comp?.name);
const generateCode = () => {
  document.getElementById("QR_code").innerHTML = "";
  // setTimeout(() => {
  new QRCode(document.getElementById("QR_code"), {
    // text: tableData.transferNo
    text: "https://www.baidu.com/",
    width: 60,
    height: 60,
  });
  // }, 1000)
};
const props = defineProps({
  value: {
    type: String,
    default: "",
  },
  list: {
    type: Array,
    default: [],
  },
});
const columns = [
  {
    title: "序号",
    dataIndex: "sortNum",
    width: 50,
  },
  {
    title: "材料编码",
    dataIndex: "goodsCode",
    width: 150,
  },
  {
    title: "材料名称",
    dataIndex: "goodsName",
    width: 200,
  },
  {
    title: "物资类别",
    dataIndex: "financeCategory_dictText",
    width: 120,
  },
  {
    title: "规格型号",
    dataIndex: "goodsSpecsDetail",
    width: 200,
    slots: { customRender: "goodsSpecsDetail" },
  },
  {
    title: "单位",
    dataIndex: "unitName",
    width: 50,
  },
  {
    title: "出库数量",
    dataIndex: "outStoreDetailNum",
    width: 60,
  },
  {
    title: "单价",
    dataIndex: "goodsPrice",
    width: 80,
  },
  {
    title: "金额(元)",
    dataIndex: "totalAmt",
    width: 80,
  },
];
const outerContainer = ref("");
const iframeRef = ref(null);
function print() {
  //赋值
  if (!iframeRef.value) {
    console.error("iframeRef is not set or invalid.");
    return;
  }
  outerContainer.value = document.getElementById("print").innerHTML;
  // 获取 iframe 内部的文档对象
  const iframeDoc =
    iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  // 创建并设置样式
  const style = iframeDoc.createElement("style");
  style.innerHTML = printStyle;
  // 将样式添加到 iframe 内部的文档中的 <head> 元素中
  iframeDoc.head.appendChild(style);
  // 将内容插入到 iframe 中
  iframeDoc.body.innerHTML = outerContainer.value;
  // generateCode()
  // nextTick(()=>{
  //   iframeRef.value.contentWindow.print()
  // })
  iframeRef.value.contentWindow.print();
}
defineExpose({
  print,
});
</script>
<style scoped lang="scss">
// .td{
//   max-height: 2em !important;
//           white-space: pre-wrap !important;
//           word-break: break-all !important;
//           width:10em !important;
//           overflow: hidden !important;
// }
</style>
