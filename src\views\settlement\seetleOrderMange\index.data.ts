import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
// import { queryStoreList } from './index.api'
import { getspdStoragelist, suppliernoPagelist } from '/@/api/common/api';
import { FormatNumToThousands } from '/@/utils/index'
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '结算单号',
    align: 'center',
    dataIndex: 'settlementBillCode',
    width: 200,
    resizable: true
  },
  {
    title: '代销结算汇总单',
    align: 'center',
    dataIndex: 'settlementTotalNo',
    width: 200,
    resizable: true
  },
  {
    title: '出库仓库',
    align: 'center',
    dataIndex: 'storageName',
    width: 180,
    resizable: true
  },
  {
    title: '结算类型',
    align: 'center',
    dataIndex: 'settlementBillType_dictText',
    width: 100,
    resizable: true
  },
  {
    title: '结算单生成日期',
    align: 'center',
    dataIndex: 'settlementBillDate',
    width: 160,
    resizable: true
  },
  {
    title: '创建日期',
    align: 'center',
    dataIndex: 'createTime',
    width: 160,
    resizable: true
  },
  {
    title: '审核日期',
    align: 'center',
    dataIndex: 'auditDate',
    width: 160,
    resizable: true
  },
  {
    title: '审核人',
    align: 'center',
    dataIndex: 'auditor',
    width: 160,
    resizable: true
  },
  {
    title: '总数量',
    align: 'center',
    dataIndex: 'totalNum',
    width: 120,
    resizable: true
  },
  {
    title: '总金额（元）',
    align: 'center',
    dataIndex: 'totalAmt',
    width: 120,
    resizable: true,
    sorter: true,
    customRender: ({ text }) => {
      return FormatNumToThousands(text);
    },

  },
  {
    title: '待开票金额',
    align: 'center',
    dataIndex: 'untreatedAmt',
    width: 120,
    resizable: true,
    sorter: true,
    customRender: ({ text }) => {
      return FormatNumToThousands(text);
    },

  },
  {
    title: '已开票金额',
    align: 'center',
    dataIndex: 'invoicedAmt',
    width: 120,
    resizable: true,
    customRender: ({ text, record, index }) => {
      return FormatNumToThousands(Number(record.totalAmt - record.untreatedAmt).toFixed(2))
    },
  },
  {
    title: '发票号',
    align: 'center',
    dataIndex: 'invoiceNo',
    width: 120,
    resizable: true,
    slots: { customRender: 'invoiceNo' },
  },
  {
    title: '供应商  ',
    align: 'center',
    dataIndex: 'supplierName',
    width: 200,
    resizable: true
  },
  {
    title: '推送状态  ',
    align: 'center',
    dataIndex: 'pushStatus_dictText',
    width: 100,
    resizable: true
  },
  {
    title: '审核状态  ',
    align: 'center',
    dataIndex: 'auditStatus_dictText',
    width: 100,
    resizable: true
  },
  {
    title: '打印次数  ',
    align: 'center',
    dataIndex: 'printCount',
    width: 100,
    resizable: true
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [

  { field: 'settlementTotalNo', component: 'JInput', label: '代销结算汇总单号', },
  { field: 'settlementBillCode', component: 'JInput', label: '结算单号', },
  {
    label: '出库仓库',
    field: 'storageId',
    component: 'ApiSelect',
    componentProps: {
      type: 'like',
      value: [],
      api: () => getspdStoragelist({ column: 'storageType', order: 'asc' }),
      showSearch: true,
      labelField: 'storageName', // label值
      optionFilterProp: 'storageNameAbbr',
      // resultField:'records', //  接口返回的字段，如果接口返回数组，可以不填。支持x.x.x格式
      valueField: 'id', // value值
      // mode: "multiple", // 支持多选
      allowClear: true
    },
  },
  { field: 'settlementBillDate', component: 'RangePicker', label: '结算单生成日期', componentProps: { valueType: 'Date' } },
  { field: 'createTime', component: 'RangePicker', label: '创建日期', componentProps: { valueType: 'Date' } },
  { field: 'auditDate', component: 'RangePicker', label: '审核日期', componentProps: { valueType: 'Date' } },
  { field: 'auditor', component: 'JInput', label: '审核人', },
  {
    label: '供应商',
    field: 'supplierId',
    component: 'ApiSelect',
    componentProps: {
      api: suppliernoPagelist,
      labelField: 'supplierName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: "supplierNameSupplyPycode",
    },
    colProps: { span: 6 },
  },
  {
    label: '审核状态',
    field: 'auditStatus',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'audit_status',
      };
    },
    colProps: {
      span: 4,
    },
  },
  {
    label: '推送状态',
    field: 'pushStatus',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'push_status',
      };
    },
  },
  {
    label: '结算类型',
    field: 'settlementBillType',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'settlementbills_type',
      };
    },

  },
  { field: 'invoiceNo', component: 'Input', label: '发票号' },
]