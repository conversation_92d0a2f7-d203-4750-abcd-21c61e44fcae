<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable"  @change="onChange">
      <template #form-purchase="{ model, field }">
        <ApiSelect :api="getSearchUserList" v-model:value="model[field]" allowClear showSearch placeholder="请选择采购员"
          optionFilterProp="label" labelField="realname" valueField="username" resultField="result" :params="searchParams"
          @search="onSearch" @click="onClick" :filterOption="false">
        </ApiSelect>
      </template>
     <template #tableTitle>
       <a-button  preIcon="ant-design:export-outlined" type="primary" @click="getExportXls" :loading="loading" v-auth="'settlement-export'">导出</a-button>
     </template>
      <template #settlementBillCode="{ record }">
        <span class="text_blue" @click="handleOpen(record)">{{ record.settlementBillCode }}</span>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </template>
    </BasicTable>
    <DetailModal v-bind="$attrs" @register="registerDetail"></DetailModal>
  </div>
</template>

<script setup name="settlement-settlementDetailsQuiry" lang="ts">
import { BasicTable } from "/@/components/Table";
import { useListPage } from "/@/hooks/system/useListPage";
import { columns, Schema } from "./index.data";
import { list ,getExportUrl } from "./index.api";
import { useModal } from "/@/components/Modal";
import DetailModal from './components/DetailModal.vue'
import { ApiSelect } from '/@/components/Form/index';
import { useApiSelect } from '/@/spdHooks/useApiSelect'
import { getSearchUserList} from '/@/api/common/api';
import { exportFile, getNumToThousands } from '/@/utils/index'
import { useMessage } from '/@/hooks/web/useMessage';
import { ref } from "vue";
const { createMessage } = useMessage()
const { onSearch, onClick, searchParams } = useApiSelect()
const [registerDetail, { openModal: detailModal }] = useModal(); // 详情modal
const getFootTotal = (currentPageData) => `${handleSummary(currentPageData).goodsNum} ,\xa0` + `${handleSummary(currentPageData).totalAmt}`

let mySorter = {
  column: "createTime",
  order: "desc",
};


//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll: { y: 420 },
    showActionColumn: false,
    showIndexColumn: true,
    immediate:false,
    formConfig: {
      schemas: Schema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      autoAdvancedCol: 3,
      alwaysShowLines: 2,
      fieldMapToNumber: [],
      fieldMapToTime: [
        ["createTime", ["createTime_begin", "createTime_end"], "YYYY-MM-DD"],
      ],
    },
    actionColumn: {
      width: 180,
      fixed: "right",
    },
    beforeFetch(params) {
      params['goodsCommon.domesticImport'] = params.domesticImport
      params['goodsCommon.financeCategory'] = undefined
      params['goodsCommon.bulkPurchaseTypeCode'] = params.bulkPurchaseTypeCode
      params['domesticImport'] = undefined
      params['financeCategory'] = params.financeCategory
      params['bulkPurchaseTypeCode'] = undefined
      params['settlementBills.pushStatus'] = params.pushStatus
      params['settlementBills'] = undefined
    }
  },
});
function handleSummary(tableData) {
  const totalAmount = tableData.reduce((prev, next) => {
    prev += Number(next.totalAmt);
    return prev;
  }, 0);
  const totalNumber = tableData.reduce((prev, next) => {
    prev += Number(next.goodsNum);
    return prev;
  }, 0);
  return {
    totalAmt: `本页总金额 : ${getNumToThousands(totalAmount)}`,
    goodsNum: `本页总数量 : ${totalNumber.toFixed(2)}`,
  }
}

const [registerTable,{getForm}] = tableContext;
const handleOpen = (_record) => {
  // detailModal(true, {
  //   record,
  // });
}

const loading = ref(false);
const onExportXls = async () => {
  let myParams = getForm().getFieldsValue();
  let params: any = { ...myParams, ...mySorter };
  params['goodsCommon.domesticImport'] = params.domesticImport
      params['goodsCommon.financeCategory'] = undefined
      params['domesticImport'] = undefined
      params['financeCategory'] = params.financeCategory
  await getExportUrl(params).then((res) => {
      exportFile(res,'结算明细');
  });
};
const getExportXls = async () => {
  loading.value = true;
  await onExportXls();
  loading.value = false;
};

const onChange = (_pagination, _filters, sorter) => {
  mySorter.column = sorter.field;
  if (sorter.order == "ascend") {
    mySorter.order = "asc";
  } else {
    mySorter.order = "desc";
  }
};

</script>

<style lang="scss" scoped>
.text_blue {
  color: #1890ff;
  cursor: pointer;
}
</style>
