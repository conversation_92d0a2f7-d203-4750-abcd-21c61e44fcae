import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getDepartListLeaf, getspdStoragelist } from '/@/api/common/api';
import { FormatNumToThousands } from '/@/utils/index'
import { JVxeTypes } from '/@/components/jeecg/JVxeTable/types';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '采购计划单号',
    align: 'center',
    dataIndex: 'purchasePlanNo',
    width: 180,
    resizable:true,
  },
  {
    title: '采购数量',
    align: 'center',
    dataIndex: 'purchasePlanNum',
    width: 100,
    sorter: true,
    resizable:true,
  },
  {
    title: '采购金额',
    align: 'center',
    dataIndex: 'totalAmt',
    width: 120,
    sorter: true,
    resizable:true,
    customRender: ({ text }) => {
      return FormatNumToThousands(text);
    },
  },
  {
    title: '响应仓库',
    align: 'center',
    dataIndex: 'storageName',
    width:150,
    resizable:true,
  },
  {
    title: '采购计划单创建人',
    align: 'center',
    dataIndex: 'makeUserRealname',
    width: 100,
    resizable:true,
  },
  {
    title: '采购计划单创建时间',
    align: 'center',
    dataIndex: 'createTime',
    sorter: true,
    width:160,
    resizable:true,
  },
  {
    title: '是否转成订单',
    align: 'center',
    dataIndex: 'orderFlag_dictText',
    width:100,
    resizable:true,
  },
  {
    title: '摘要',
    align: 'center',
    dataIndex: 'purchaseRemark',
    width:120,
    slots: { customRender: 'purchaseRemark' },
    resizable:true,
  },
  {
    title: '推送人',
    align: 'center',
    dataIndex: 'pushPerson',
    width:80,
  },
  {
    title: '推送时间',
    align: 'center',
    dataIndex: 'pushTime',
    width:120,
  },
];
//查询数据
export const Schema: FormSchema[] = [
  { field: 'purchasePlanNo', component: 'JInput', label: '采购计划单号', },
  {
    field: 'makeUserName',
    component: 'Select',
    label: '采购计划单创建人',
    slot: 'maker',
  },
  { field: 'createTime', component: 'RangePicker', label: '采购计划单创建时间', componentProps: { valueType: 'Date'},},
  // { field: '3', component: 'JInput', label: '需求汇总单单号', },
  {
    label: '是否转成订单',
    field: 'orderFlag',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
  {
    label: '响应仓库',
    field: 'storageId',
    component: 'ApiSelect',
    componentProps: {
      type: 'like',
      value: [],
      api: () => getspdStoragelist({}),
      showSearch: true,
      optionFilterProp: 'storageNameAbbr',
      labelField: 'storageName',
      valueField: 'id', 
      allowClear: true,
    },
  },
  {
    field: 'pushPerson',
    component: 'Select',
    label: '推送人',
    slot: 'pushPerson',
  },
  { field: 'pushTime', component: 'RangePicker', label: '推送时间', componentProps: { valueType: 'Date'},},
]

export const detailColumns: BasicColumn[] = [

  {
    title: '物资编号',
    align: 'center',
    dataIndex: 'goodsCode',
    width: 150
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 150
  },
  {
    title: '申领科室',
    align: 'center',
    dataIndex: 'departName',
    width: 150
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width: 100,
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
  },
  {
    title: '计量单位',
    align: 'center',
    dataIndex: 'unitName',
    width: 80,
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'goodsPrice',
    width: 80,
  },
  {
    title: '采购数量',
    align: 'center',
    dataIndex: 'purchaseNum',
    width: 180,
  },
  {
    title: '科室备注',
    dataIndex: 'departRemarkStr',
    width: 120,
    slots: { customRender: 'departRemarkStr' },
  },
  {
    title: '金额  ',
    align: 'center',
    dataIndex: 'totalAmt',
    width: 180,
  },
  {
    title: '包装数量',
    align: 'center',
    dataIndex: 'minPackageNum',
    width: 80,
  },
  {
    title: '包装单位',
    dataIndex: 'packageUnit',
    width: 100,
  },
  {
    title: '定数包规格',
    dataIndex: 'quantitativePackageSpecs',
    width: 120,
  },
  {
    title: '定数包数量',
    dataIndex: 'packageNum',
    width: 100,
  },
  {
    title: '响应仓库  ',
    align: 'center',
    dataIndex: 'outStorageName',
  },
  {
    title: '采购类型  ',
    align: 'center',
    dataIndex: 'purchaseAttribute_dictText',
    width: 100,
  },
  {
    title: '需求类型  ',
    align: 'center',
    dataIndex: 'typeOfRequirement_dictText',
    width: 100,
  },
  {
    title: '供应商  ',
    align: 'center',
    dataIndex: 'supplierName',
  },
  {
    title: '是否临采',
    dataIndex: 'tempPurchaseFlag_dictText',
    width: 120,
  },
  {
    title: '到货日期',
    align: 'center',
    dataIndex: 'requestDate',
  },
  {
    title: '送货地址',
    align: 'center',
    dataIndex: 'deliveryAddress',
  },
  {
    title: '资金来源',
    align: 'center',
    dataIndex: 'fundSource_dictText',
  },
];
export const planColumns= [
  {
    title: '物资编码',
    key: 'goodsCode',
    type: JVxeTypes.slot,
    width: 150,
    align: 'center',
    slotName: 'goodsCode',
  },
  {
    title: '物资名称',
    key: 'goodsName',
    type: JVxeTypes.slot,
    width: 150,
    align: 'center',
    slotName: 'goodsName',
  },
  {
    title: '规格',
    key: 'goodsSpecs',
    type: JVxeTypes.slot,
    width: 150,
    align: 'center',
    slotName: 'goodsSpecs',
  },
  {
    title: '型号',
    key: 'goodsSpecsDetail',
    type: JVxeTypes.slot,
    width: 250,
    align: 'center',
    slotName: 'goodsSpecsDetail',
  },
  {
    title: '单位',
    key: 'unitName',
    width: 80,
    align: 'center',
  },
  {
    title: '单价',
    key: 'goodsPrice',
    width: 100,
    align: 'center',
  },
  {
    title: '采购数量',
    key: 'purchaseNum',
    type: JVxeTypes.slot,
    width: 150,
    align: 'center',
    slotName: 'purchaseNum',
  },
  {
    title: '科室备注',
    key: 'departRemarkStr',
    type: JVxeTypes.slot,
    width: 180,
    align: 'center',
    slotName: 'departRemarkStr',
  },
  {
    title: '金额',
    key: 'totalAmt',
    width: 100,
    align: 'center',
  },
  {
    title: '定数包规格',
    key: 'quantitativePackageList',
    type: JVxeTypes.slot,
    width: 150,
    align: 'center',
    slotName: 'quantitativePackageList',
  },
  {
    title: '定数包数量',
    key: 'packageNum',
    type: JVxeTypes.slot,
    width: 100,
    align: 'center',
    slotName: 'packageNum',
  },
  {
    title: '物资包装单位',
    key: '1',
    width: 120,
    align: 'center',
  },
  {
    title: '物资包装数量',
    key: '2',
    width: 120,
    align: 'center',
  },
  {
    title: "品牌",
    key: "brand",
    width: 150,
    align: 'center',
  },
  {
    title: '供应商',
    key: 'supplierName',
    width: 150,
    align: 'center',
  },
  {
    title: '生产厂商',
    key: 'manufacturerName',
    width: 150,
    align: 'center',
  },
  {
    title: '是否临采',
    key: 'tempPurchaseFlag_dictText',
    width: 100,
    align: 'center',
  },
  {
    title: '临采上次采购时间',
    key: 'tempPurchaseTime',
    width: 150,
    align: 'center',
  },
  {
    title: '资金来源',
    key: 'fundSource_dictText',
    width: 100,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    type: JVxeTypes.slot,
    fixed: 'right',
    minWidth: 170,
    align: 'center',
    slotName: 'action',
  },
];
export const searchColumns = [
  {
    title: '物资编码',
    dataIndex: 'goodsCode',
    width: 150,
  },
  {
    title: '物资名称',
    dataIndex: 'goodsName',
    width: 150,
  },
  {
    title: '别名',
    dataIndex: 'goodsCommonName',
    width: 100,
  },
  {
    title: '规格',
    dataIndex: 'goodsSpecs',
    width: 250,
  },
  {
    title: '型号',
    dataIndex: 'goodsSpecsDetail',
    width: 100,
  },
  {
    title: '计量单位',
    dataIndex: 'unitName',
    width: 100,
  },
  {
    title: '单价',
    dataIndex: 'goodsPrice',
    width: 100,
  },
  {
    title: "品牌",
    dataIndex: "brand",
    width: 150,
  },
  {
    title: "生产厂商",
    dataIndex: "manufacturerName",
    width: 250,
  },
];
// 订单列表
export const orderColumns = [
  {
    title: '订单号',
    dataIndex: 'orderNo',
    width: 120,
  },
  {
    title: '供应商',
    dataIndex: 'supplierName',
    width: 120,
  },
  {
    title: '总数量',
    dataIndex: 'orderNum',
    width: 100,
  },
  {
    title: '金额',
    dataIndex: 'totalAmt',
    width: 120,
  },
];
export const orderChildColumns = [
  {
    title: '物资编码',
    dataIndex: 'goodsCode',
  },
  {
    title: '物资名称',
    dataIndex: 'goodsName',
  },
  // {
  //   title: '别名',
  //   dataIndex: 'goodsCommonName',
  //   width: 100,
  // },
  {
    title: '规格',
    dataIndex: 'goodsSpecs',
  },
  {
    title: '型号',
    dataIndex: 'goodsSpecsDetail',
  },
  {
    title: '计量单位',
    dataIndex: 'unitName',
  },
  {
    title: '单价',
    dataIndex: 'goodsPrice',
  },
  {
    title: "品牌",
    dataIndex: "brand",
  },
  {
    title: "生产厂商",
    dataIndex: "manufacturerName",
  },
  {
    title: "供应商",
    dataIndex: "supplierName",
  },
];