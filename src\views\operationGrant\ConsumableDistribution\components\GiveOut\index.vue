<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :default-fullscreen="true" width="1500px" :showCancelBtn="true"
    :showOkBtn="true" @ok="showConfirm()" okText="确认发放" @cancel="showCancel()" :maskClosable="false">
    <div>
      <a-typography-title style="font-size: 35px; margin-left: 35%">手术物资发放</a-typography-title> <a-typography-title
        style="font-size: 18px; margin-left: 20px">手术编号：{{ oldData ? oldData.applyPlaneCode : "" }}</a-typography-title>
    </div>
    <div>
      <BasicForm @register="registerForm">
        <template #Barcode="{ model, field }">
          <a-input ref="refInput" v-model:value="model[field]" @pressEnter="InputPressEnter(model[field])" />
        </template>
      </BasicForm>
    </div>
    <div>
      <BasicTable @register="registerTable" class="ant-table-striped" :rowClassName="rowClassName">
        <template #footer="currentPageData">
          <div class="foot pd6 dpflex jcsb">
            <div>合计</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
          </div>
        </template>
      </BasicTable>
    </div>
    <div>
      <BasicTable @register="registerTable2" class="ant-table-striped" :rowClassName="rowClassName">
        <template #action="{ record }">
          <TableAction :actions="[
            {
              label: '删除',
              onClick: handleDelete.bind(null, record),
            },
          ]" />
        </template>
      </BasicTable>
    </div>
  </BasicModal>
  <!-- 汇总 -->
  <SummarizingModel @register="registerModalSummarizing"></SummarizingModel>
</template>

<script lang="ts" setup>
import { BasicForm, useForm } from "/@/components/Form/index";
import { BasicTable, useTable, TableAction } from "/@/components/Table";
import { formSchema, formTableGrant, TableGrant } from "./index.data";
import { grantList, EwmGrant, getsendOperationSuppliers, print, queryById } from "./index.api";
import { BasicModal, useModal, useModalInner } from "/@/components/Modal";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { ref, createVNode, unref, computed } from "vue";
import { Modal } from "ant-design-vue";
import { message } from "ant-design-vue";
import { getNumToThousands } from "/@/utils/index";
import SummarizingModel from "../Summarizing/index.vue"
//变量声明
let Data: any = ref();
let oldData: any = ref();
let dataSource: any = ref([]);
let dataSourcePlan: any = ref([]);
const refInput = ref();
const getFootTotal = (currentPageData) =>
  `${handleSummary(currentPageData).recoveryNum} ,\xa0` +
  `${handleSummary(currentPageData).sendNum},\xa0` +
  `${handleSummary(currentPageData).planNum},\xa0` +
  `${handleSummary(currentPageData).AftersendNum}`;
// Emits声明
const emit = defineEmits(["register", "success"]);
const rowClassName = computed(() => {
  return (record, index: number) => (record.noAppType == 1 ? "darkBlue" : "");
});
const isOperationProvideCheck = ref()
const [registerModalSummarizing, { openModal: openModalSummarizing }] = useModal();
//表单赋值
const [registerModal, { setModalProps, closeModal, changeOkLoading }] = useModalInner(async (data) => {
  let storage = await queryById({ id: data.record.storageId })
  isOperationProvideCheck.value = storage.isOperationProvideCheck
  let res = await grantList({ applyOrderId: data.record.id });
  dataSourcePlan.value = res;
  //重置表单
  await resetFields();
  setModalProps({ confirmLoading: false, });
  Data.value = !data?.isUpdate;
  oldData.value = data.record;
  if (unref(Data)) {
    //表单赋值
    await setFieldsValue({
      ...data.record,
    });
  }
  refInput.value.focus();
});
//表单配置
const [registerTable] = useTable({
  dataSource: dataSourcePlan,
  rowKey: "id",
  bordered: true,
  columns: formTableGrant,
  pagination: false,
  scroll: { y: 400 },
});
//表格配置
const [registerTable2] = useTable({
  dataSource,
  rowKey: "id",
  bordered: true,
  columns: TableGrant,
  pagination: false,
  scroll: { y: 400 },
  actionColumn: {
    width: 100,
    title: "操作",
    dataIndex: "action",
    slots: { customRender: "action" },
    fixed: "right",
  },
});
//表单配置
const [registerForm, { validate, resetFields, setFieldsValue }] = useForm({
  labelWidth: 100,
  baseColProps: { span: 8 },
  schemas: formSchema,
  showResetButton: false,
  autoSubmitOnEnter: true,
  showSubmitButton: false,
  rulesMessageJoinLabel: true,
  autoFocusFirstItem: true,
  compact: true,
});





async function InputPressEnter(registeredCapital) {
  registeredCapital = registeredCapital.split(/["']/)[0]
  await setFieldsValue(
    {
      registeredCapital
    }
  )
  changeOkLoading(false);
  if (registeredCapital.length == 24 || registeredCapital.length == 25) {
    await handleSubmit();
  } else {
    message.error("条码不正确，请重新扫码");
    //重置表单
    setTimeout(async () => {
      await resetFields();
      if (unref(oldData)) {
        //表单重新赋值
        await setFieldsValue({
          ...oldData.value,
        });
      }
    }, 100);
  }
}

function handleSummary(tableData: Recordable[]) {
  // 已回收总数
  const totalAmt = tableData.reduce((prev, next) => {
    prev += Number(next.recoveryNum);
    return prev;
  }, 0);
  // 已发放总数
  const totalNumber = tableData.reduce((prev, next) => {
    prev += Number(next.sendNum ? next.sendNum : 0);
    return prev;
  }, 0);
  // 计划发放总数
  const planNumber = tableData.reduce((prev, next) => {
    prev += Number(next.planeNum ? next.planeNum : 0);
    return prev;
  }, 0);
  //本次发放总数
  const AftersendNumber = tableData.reduce((prev, next) => {
    prev += Number(next.AftersendNum ? next.AftersendNum : 0);
    return prev;
  }, 0);
  return {
    recoveryNum: `已回收总数量 : ${getNumToThousands(totalAmt)}`,
    sendNum: `已发放总数量 : ${totalNumber.toFixed(2)}`,
    planNum: `计划总数量 : ${planNumber.toFixed(2)}`,
    AftersendNum: `本次发放总数量 : ${getNumToThousands(AftersendNumber) !== 'NaN' ? getNumToThousands(AftersendNumber) : 0}`,
  };
}

//扫码提交后更新表单
async function handleSubmit() {
  const values = await validate();
  let obj = await EwmGrant({ uniqueCode: values.registeredCapital, responseStorageId: oldData.value.storageId });
  let index: any = -1;
  for (let i = 0; i < dataSourcePlan.value.length; i++) {
    if (dataSourcePlan.value[i].goodsCode == obj.goodsCode) {
      index = i;
    }
  }
  let existBool = true;
  for (let i = 0; i < dataSource.value.length; i++) {
    if (dataSource.value[i].id == obj.id) {
      existBool = false;
    }
  }

  if (existBool) {

    let term = new Date(obj.term);
    let daysNum = ((term.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))

    let termBoolA = false
    let termBoolB = false
    if (daysNum <= 180 && daysNum > 0) {
      termBoolA = true
    }
    let dataSourceCopy = JSON.parse(JSON.stringify(dataSource.value))
    dataSourceCopy.push(obj);
    await getTermSort(dataSourceCopy)
    let arr = dataSourceCopy.filter(item => item.id == obj.id)
    termBoolB = arr[0].termSortBool



    // 封装通用确认弹窗逻辑
    const showConfirmModal = (content: string, onOkCallback: () => void) => {
      Modal.confirm({
        title: () => "提示",
        icon: () => createVNode(ExclamationCircleOutlined),
        content: () =>
          createVNode("div", { style: "color:#000;" }, content),
        onOk: onOkCallback,
        onCancel() {
          refInput.value.focus();
        },
        class: "test",
      });
    };
    
    // 封装表单重置逻辑
    const resetForm = async () => {
      try {
        await resetFields();
        if (unref(oldData)) {
          await setFieldsValue({ ...oldData.value });
        }
      } catch (error) {
        console.error("表单重置失败:", error);
      }
    };
    
    // 主逻辑
    if (termBoolA && !termBoolB) {
      showConfirmModal(
        `此物资为近效期物资，还有${Math.floor(daysNum)}天过期，是否确认继续发放？`,
        () => handleDistribution(index, obj)
      );
    } else if (!termBoolA && termBoolB) {
      showConfirmModal(
        `此物资有更近效期物资${obj.firstTerm}，是否确认继续发放当前物资？`,
        () => handleDistribution(index, obj)
      );
    } else if (termBoolA && termBoolB) {
      showConfirmModal(
        `此物资有更近效期的物资${obj.firstTerm}，此物资还有${Math.floor(daysNum)}天过期，是否确认继续发放？`,
        () => handleDistribution(index, obj)
      );
    } else {
      handleDistribution(index, obj);
    }
    
    setTimeout(resetForm, 100);
    
    // 提取更新数据源逻辑
    function updateDataSource (obj: any) {
      obj.noAppType = 1;
      dataSourcePlan.value.push(obj);
      dataSource.value.push(obj);
      const lastIndex = dataSourcePlan.value.length - 1;
      dataSourcePlan.value[lastIndex].AftersendNum = 1;
      dataSourcePlan.value[lastIndex].planeNum = 0;
      dataSourcePlan.value[lastIndex].recoveryNum = 0;
    };
    // 抽取核心分发逻辑
    function handleDistribution  (index: number, obj: any) {
      if (index === -1) {
        if (isOperationProvideCheck.value === 1) {
          showConfirmModal("该物资未在计划列表中是否发放？", () => {
            updateDataSource(obj);
            refInput.value.focus();
          });
        } else {
          updateDataSource(obj);
          refInput.value.focus();
        }
      } else {
        let j:any = -1;
        const goodsMap = new Map(dataSourcePlan.value.map((item, i) => [item.goodsCode, i]));
        if (goodsMap.has(obj.goodsCode)) {
          j = goodsMap.get(obj.goodsCode)!;
          if (Number(dataSourcePlan.value[j].planeNum) === 0) {
            obj.noAppType = 1;
          }
        }
    
        const PlaneNum = Number(dataSourcePlan.value[index].planeNum);
        const SendNum = Number(dataSourcePlan.value[index].sendNum);
        const AftersendNum = j > -1 ? Number(dataSourcePlan.value[j].AftersendNum || 0) : 0;
    
        if (SendNum + AftersendNum === PlaneNum) {
          showConfirmModal("已满足该物资计划数量，是否继续发放？", () => {
            if (j > -1) {
              dataSourcePlan.value[j].AftersendNum = (dataSourcePlan.value[j].AftersendNum || 0) + 1;
            } else {
              dataSourcePlan.value.push(obj);
              const lastIndex = dataSourcePlan.value.length - 1;
              dataSourcePlan.value[lastIndex].AftersendNum = 1;
              dataSourcePlan.value[lastIndex].planeNum = 0;
              dataSourcePlan.value[lastIndex].recoveryNum = 0;
            }
            dataSource.value.push(obj);
            refInput.value.focus();
          });
        } else {
          if (j > -1) {
            dataSourcePlan.value[j].AftersendNum = (dataSourcePlan.value[j].AftersendNum || 0) + 1;
          } else {
            dataSourcePlan.value.push(obj);
            const lastIndex = dataSourcePlan.value.length - 1;
            dataSourcePlan.value[lastIndex].AftersendNum = 1;
            dataSourcePlan.value[lastIndex].planeNum = 0;
            dataSourcePlan.value[lastIndex].recoveryNum = 0;
          }
          dataSource.value.push(obj);
        }
      }
    };
    

  } else {
    message.warning("该物资已扫码添加");
  }
}




function getTermSort(list) {
  const groupedArray = groupByProperty(list, 'goodsCode');
  groupedArray.forEach((item) => {
    let num: any = findMissingElements(item)[0] ? findMissingElements(item)[0] : 1;
    item.sort((a, b) => { return a.termSort - b.termSort; });
    const arr = groupByProperty(item, 'termSort');
    let counterBool = false
    arr.forEach((item) => {
      for (let i = 0; i < item.length; i++) {
        if (item[i].termSort <= num && counterBool == false) {
          item[i].termSortBool = false;
        } else {
          item[i].termSortBool = true;
        }
      }
      if (item.length == item[0].termCurrentNum) {
        counterBool = false
      } else {
        counterBool = true
      }
    });
  });
}
function groupByProperty(arr, property) {
  const map = new Map();
  arr.forEach((item) => {
    const key = item[property];
    if (!map.has(key)) {
      map.set(key, []);
    }
    map.get(key).push(item);
  });

  return Array.from(map.values());
}
function findMissingElements(arr) {
  if (arr.length === 0) return [];
  // 提取并排序num属性
  const nums = arr.map((item) => item.termSort).sort((a, b) => a - b);
  const missing = [];
  // 始终从1开始
  const min = 1;
  const max = nums[nums.length - 1];
  // 查找缺失的数字（从1开始）
  for (let i = min; i <= max; i++) {
    if (!nums.includes(i)) {
      missing.push(i);
    }
  }
  // 如果没有缺失的元素，返回最后一个值+1
  if (missing.length === 0 && nums.length > 0) {
    return [nums[nums.length - 1] + 1];
  }

  return missing;
}

//确认发放事件
async function showConfirm() {
  changeOkLoading(true);

  let array: any = [];
  for (let i = 0; i < dataSource.value.length; i++) {
    array.push(dataSource.value[i].id);
  }
  await getsendOperationSuppliers({
    ids: array,
    applyOrderId: oldData.value.id,
    sendOrRecycleType: 0,
  }).then((result) => {
    if (result) {
      if (array.length > 0) {
        Modal.confirm({
          title: () => "是否打印发放单?",
          icon: () => createVNode(ExclamationCircleOutlined),
          content: () => createVNode("div", { style: "color:red;" }),
          okText: "打印",
          cancelText: "取消",
          maskClosable: true,
          async onOk() {
            openModalSummarizing(true, {
              record: oldData.value,
              isUpdate: true,
              showFooter: false,
            });
          },
          async onCancel() { },
          class: "test",
        });
      }
    }
  })
  //刷新列表
  emit("success");
  dataSource.value = [];
  closeModal();
  changeOkLoading(false);
}

//取消事件
function showCancel() {
  dataSource.value = [];
  closeModal();
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  for (let i = 0; i < dataSourcePlan.value.length; i++) {
    if (dataSourcePlan.value[i].goodsCode == record.goodsCode) {
      dataSourcePlan.value[i].AftersendNum--;
    }
  }
  for (let i = 0; i < dataSource.value.length; i++) {
    if (dataSource.value[i].id == record.id) {
      dataSource.value.splice(i, 1);
    }
  }
}


</script>

<style lang="less" scoped>
/** 时间和数字输入框样式 */
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-calendar-picker) {
  width: 100%;
}

.ant-table-striped :deep(.darkBlue) td {
  background: rgb(243, 204, 166);
}
</style>
