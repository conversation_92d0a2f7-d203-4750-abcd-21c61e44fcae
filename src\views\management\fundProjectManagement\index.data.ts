import { BasicColumn, FormSchema } from '/@/components/Table';
export const columns: BasicColumn[] = [
  {
    title: '收费项目编码',
    align: 'center',
    dataIndex: 'chargeItemsCode',
  },
  {
    title: '收费项目名称',
    align: 'center',
    dataIndex: 'chargeItemsName',
  },
  {
    title: '收费项目价格',
    align: 'center',
    dataIndex: 'ordinarySum',
  },
  {
    title: '启用状态',
    align: 'center',
    dataIndex: 'isEnable_dictText',
  },
  {
    title: '更新时间',
    align: 'center',
    dataIndex: 'updateTime',
  },
  {
    title: '更新人',
    align: 'center',
    dataIndex: 'updateRealname',
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'chargeItemsCode',
    component: 'JInput',
    label: '收费项目编码',
  },
  {
    field: 'chargeItemsName',
    component: 'JInput',
    label: '收费项目名称',
  },
  {
    field: 'isEnable',
    component: 'JDictSelectTag',
    label: '启用状态',
    componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
];
