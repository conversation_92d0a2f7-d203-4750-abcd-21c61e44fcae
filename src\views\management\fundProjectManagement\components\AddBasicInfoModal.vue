<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit" destroyOnClose :maskClosable="false">
    <BasicForm @register="registerForm">
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicModal, useModalInner } from "/@/components/Modal";
import { BasicForm, useForm } from '/@/components/Form/index';
import { add } from '../index.api'
const formSchema = [
  {
    field: 'chargeItemsCode',
    label: '收费项目编码',
    component: 'Input',
    required: true,
  },
  {
    field: 'chargeItemsName',
    label: '收费项目名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'ordinarySum',
    label: '收费项目单价',
    component: 'Input',
    required: true,
  },
  {
    field: 'isEnable',
    component: 'JDictSelectTag',
    label: '启用状态',
    required: true,
    componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
]
const $emit = defineEmits(['success']);
const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
  schemas: formSchema,
  showActionButtonGroup: false,
});
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ title: '', width: 800 })
})

const handleSubmit = async () => {
  try {
    const values = await validate();
    await add(values);
    closeModal();
    $emit('success')
  } catch (err) {
  } finally {
    setModalProps({ confirmLoading: false });
  }
};
</script>

<style lang="scss" scoped></style>
