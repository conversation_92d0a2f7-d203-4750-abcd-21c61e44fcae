<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #goodsCode="{ model, field }">
        <TableSelect v-model:modelValue="model[field]" :columns="goodsColumns" :fetch-api="getSpdGoodsCommonlist"
          search-key="goodsCode" placeholder="请选择物资编码" @select="handleGoodsSelect" :disabled="isEdit" />
      </template>

      <template #chargeItemsName="{ model, field }">
        <TableSelect v-model:modelValue="model[field]" :columns="chargeItemsColumns" :fetch-api="chargeItemsList"
          search-key="chargeItemsName__chargeItemsCode_orFlag" placeholder="请选择收费项目"
          @select="handleChargeItemsSelect" />
      </template>

    </BasicForm>
    <template #appendFooter>
      <a-button :loading="loading" type="primary" @click="handleSavePush">保存并推送his</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form/index';
import TableSelect from '/@/components/myComponents/SelectTable.vue';
import { chargeItemsList, addBatch, pushBatch } from '../index.api';
import { getSpdGoodsCommonlist } from '/@/api/common/api';
import { useMessage } from '/@/hooks/web/useMessage';
import type { TableColumnType } from 'ant-design-vue';
const { createMessage, createConfirm } = useMessage();
const emits = defineEmits(['success']);

const formSchema = [
  {
    field: 'goodsCode',
    component: 'JDictSelectTag',
    label: '物资编码',
    required: true,
    slot: 'goodsCode'
  },
  {
    field: 'chargeItemsName',
    component: 'JDictSelectTag',
    label: '收费项目',
    required: true,
    slot: 'chargeItemsName'
  },
  {
    field: 'itemStatus',
    component: 'JDictSelectTag',
    label: '项目状态',
    required: true,
    componentProps: () => ({
      dictCode: 'charge_items_status',
    }),
  },
];

const goodsColumns: TableColumnType[] = [
  { title: "物资编码", width: 130, dataIndex: "goodsCode" },
  { title: "物资名称", width: 120, dataIndex: "goodsName" },
  { title: "规格", width: 120, dataIndex: "goodsSpecs" },
  { title: "型号", width: 120, dataIndex: "goodsSpecsDetail" },
  { title: "单价", width: 120, dataIndex: "goodsPrice" },
];

const chargeItemsColumns: TableColumnType[] = [
  { title: "收费项目编码", width: 120, dataIndex: "chargeItemsCode" },
  { title: "收费项目名称", width: 120, dataIndex: "chargeItemsName" },
  { title: "价格（元）", width: 120, dataIndex: "ordinarySum" }
];

const goodsIds = ref<string>('');
const selectedChargeItem = ref<any>(null);
const isEdit = ref(false);
const [registerForm, { setFieldsValue, validate, getFieldsValue, clearValidate }] = useForm({
  labelWidth: 150,
  baseColProps: { span: 24 },
  schemas: formSchema,
  showActionButtonGroup: false
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  // 编辑
  if (data.record && data.record.selectedRows.length === 1) {
    isEdit.value = true
    goodsIds.value = data.record.selectedRows.map(v => v.goodsId)[0];
    const record = data.record.selectedRows[0];
    selectedChargeItem.value = record.chargeItemsId;
    setFieldsValue({ goodsCode: record.goodsCode, chargeItemsName: record.chargeItemsName, itemStatus: `${record.itemsStatus}` });
    setModalProps({ width: 1000, title: '编辑收费项目材料编码对应设置', okText: '保存' });
  } else {
    // 新增
    isEdit.value = false
    setFieldsValue({ goodsCode: undefined, chargeItemsName: undefined, itemStatus: undefined });
    await clearValidate()
    setModalProps({ width: 1000, title: '新增收费项目材料编码对应设置', okText: '保存' });
  }
})
const handleGoodsSelect = (record: Record<string, any>) => {
  goodsIds.value = record.id
  setFieldsValue({ goodsCode: record.goodsCode, });
};

const handleChargeItemsSelect = (record: Record<string, any>) => {
  selectedChargeItem.value = record.id
  setFieldsValue({ chargeItemsName: record.chargeItemsName });
};

interface BatchRequestData {
  itemStatus: string;
  chargeItemsId: string;
  goodsId: string;
  saveFlag?: number;
}

const handleSubmit = async () => {
  try {
    await validate();
    const values = getFieldsValue();
    setModalProps({ loading: true, confirmLoading: true });

    const batchData: BatchRequestData = {
      itemStatus: values.itemStatus,
      chargeItemsId: unref(selectedChargeItem),
      goodsId: unref(goodsIds),
    };

    const res = await addBatch([batchData]);

    if (res.data.result.length) {
      createMessage.success('保存成功');
      closeModal();
      emits('success');
    } else {
      await handleSaveConfirmation(batchData, res.data.message, false);
    }
  } catch (error) {
    console.log(error,'error');
  } finally {
    setModalProps({ loading: false, confirmLoading: false });
  }
};

const handleSaveConfirmation = (batchData: BatchRequestData, message: string, needPush = false) => { //needPush 是否需要推送
  createConfirm({
    content: message,
    iconType: 'warning',
    maskClosable: false,
    keyboard: false,
    onOk: async () => {
      try {
        const res = await addBatch([{ ...batchData, saveFlag: 1 }]);
        if (res?.data?.message) {
          createMessage.success(res.data.message);
        }
        if (needPush && res?.data?.result?.length) {
          await pushBatch({ chargeItemsRelationIds: res.data.result });
          createMessage.success('推送成功');
        }
        closeModal();
        emits('success');
      } catch (error) {
        console.log(error,'error');
      }
    },
    onCancel: () => { },
  });
};

const loading = ref(false);
const handleSavePush = async () => {
  try {
    loading.value = true;
    await validate();
    const values = getFieldsValue();
    setModalProps({ loading: true });

    const batchData: BatchRequestData = {
      itemStatus: values.itemStatus,
      chargeItemsId: unref(selectedChargeItem),
      goodsId: unref(goodsIds),
    };

    const res = await addBatch([batchData]);

    if (res.data.result.length) {
      await pushBatch({ chargeItemsRelationIds: res.data.result });
      createMessage.success('保存并推送成功');
      closeModal();
      emits('success');
    } else {
      await handleSaveConfirmation(batchData, res.data.message, true);
    }
  } catch (error) {
    console.log(error,'error');
  } finally {
    setModalProps({ loading: false });
    loading.value = false;
  }
};
</script>

<style lang="sass" scoped></style>
