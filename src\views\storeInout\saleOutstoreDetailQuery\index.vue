<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" @change="onChange">
      <template #tableTitle>
        <a-button preIcon="ant-design:export-outlined" type="primary" @click="getExportXls" :loading="loading"
          v-auth="'saleOutstore-export'">导出</a-button>
      </template>
      <template #consignmentNo="{ record }">
        <span @click="handleOpen(record)">{{ record.consignmentNo }}</span>
      </template>
      <template #form-outUser="{ model, field }">
        <ApiSelect :api="getSearchUserList" v-model:value="model[field]" allowClear showSearch placeholder="请输入制单人"
          optionFilterProp="label" labelField="realname" valueField="username" resultField="result"
          :params="searchParams" @search="onSearch" @click="onClick" :filterOption="false">
        </ApiSelect>
      </template>
      <template #form-depart="{ model, field }">
        <a-select v-model:value="model[field]" placeholder="请选择执行科室" :allowClear="isShow" showSearch
          optionFilterProp="departNameDepartNameAbbr" :options="departList">
        </a-select>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </template>
    </BasicTable>
  </div>
</template>

<script setup name="storeInout-saleOutstoreDetailQuery" lang="ts">
import { BasicTable } from "/@/components/Table";
import { useListPage } from "/@/hooks/system/useListPage";
import { columns, Schema } from "./index.data";
import { list, getExportUrl ,getDeliveryspdStoragelist} from "./index.api";
import { ApiSelect } from '/@/components/Form/index';
import { useApiSelect } from '/@/spdHooks/useApiSelect'
import { getSearchUserList, getDepartListLeaf, queryMyDeptListByTenantId, getspdStoragelist } from '/@/api/common/api';
import { exportFile, getNumToThousands } from '/@/utils/index'
import { onMounted, ref, computed, unref } from "vue";
import { usePermission } from '/@/hooks/web/usePermission';
import { useUserStore } from '/@/store/modules/user';




const userStore: any = useUserStore();
const code = (userStore.hospitalZoneInfo?.depart?.orgCode);
const storageId = (userStore.hospitalZoneInfo?.storage?.id);

const props = defineProps({
  formtype: {
    type: String,
    default: '0'
  }
})
const { hasPermission } = usePermission();
const curDepartList = ref<any>([]) //当前登录科室列表
const allDepartList = ref<any>([]) //所有科室列表
const departList = computed(() => {
  return hasPermission('saleOutstoreDetailQuery:depart') ? unref(curDepartList) : unref(allDepartList)
})
const isShow = computed(() => hasPermission('saleOutstoreDetailQuery:depart') ? false : true)
const getDepartList = async () => {
  const res = await getDepartListLeaf({})
  allDepartList.value = res.map(v => {
    return {
      label: v.departName,
      value: v.id,
      departNameDepartNameAbbr: v.departNameDepartNameAbbr
    }
  })
}
const getCurDepartList = async () => {
  const res = await queryMyDeptListByTenantId({ delFlag: 0 })
  curDepartList.value = res.map(v => {
    return {
      label: v.departName,
      value: v.id,
      departNameDepartNameAbbr: v.departName + v.departNameAbbr
    }
  })
}
const init = () => {
  getCurDepartList()
  getDepartList()
}
onMounted(() => {


  if (props.formtype == '1') {
    getForm().updateSchema({
      label: '出库仓库',
      field: 'outStorageId',
      component: 'ApiSelect',
      componentProps: {
        api: () => getDeliveryspdStoragelist({ departCode: code }),
        params: {
          //parentId:userStore.getHospitalZoneInfo.storage.id
        },
        showSearch: true,
        optionFilterProp: 'storageNameAbbr',
        labelField: 'storageName',
        valueField: 'id',
      },
      defaultValue: storageId,
      required: true,
    })
  } else {
    getForm().updateSchema({
      label: '出库仓库',
      field: 'outStorageId',
      component: 'ApiSelect',
      componentProps: {
        type: 'like',
        value: [],
        api: () => getspdStoragelist({}),
        showSearch: true,
        optionFilterProp: 'storageNameAbbr',
        labelField: 'storageName',
        valueField: 'id',
        allowClear: true
      },
    })
  }

  init()
})
const { onSearch, onClick, searchParams } = useApiSelect()
const getFootTotal = (currentPageData) => `${handleSummary(currentPageData).goodsNum} ,\xa0` + `${handleSummary(currentPageData).totalAmt}`
let mySorter = {
  column: "createTime",
  order: "desc",
};

//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll: { y: 480 },
    maxHeight: 480,
    showActionColumn: false,
    showIndexColumn: true,
    immediate:false,
    formConfig: {
      labelCol: {
        xxl: 8,
      },
      //是否显示展开收起按钮，默认false
      showAdvancedButton: false,
      //超过指定行数折叠，默认3行
      autoAdvancedCol: 3,
      //折叠时默认显示行数，默认1行
      alwaysShowLines: 2,
      schemas: Schema,
      size: "small",
      autoSubmitOnEnter: true,
      fieldMapToNumber: [],
      fieldMapToTime: [
        ["createTime", ["createTime_begin", "createTime_end"], "YYYY-MM-DD"],
      ],
    },
    actionColumn: {
      width: 180,
      fixed: "right",
    },
    beforeFetch(params) {
      params['goodsCommon.bulkPurchaseTypeCode'] = params.bulkPurchaseTypeCode
      params['bulkPurchaseTypeCode'] = undefined
    }
  },
});
function handleSummary(tableData) {
  const totalAmount = tableData.reduce((prev, next) => {
    prev += Number(next.totalAmt);
    return prev;
  }, 0);
  const totalNumber = tableData.reduce((prev, next) => {
    prev += Number(next.goodsNum);
    return prev;
  }, 0);
  return {
    totalAmt: `本页总金额 : ${getNumToThousands(totalAmount)}`,
    goodsNum: `本页总数量 : ${totalNumber.toFixed(2)}`,
  }
}
const [registerTable, { getForm }] = tableContext;
const handleOpen = (_record) => {
}

const loading = ref(false);
const onExportXls = async () => {
  let myParams = getForm().getFieldsValue();
  let params: any = { ...myParams, ...mySorter };
  await getExportUrl(params).then((res) => {
    exportFile(res, '代销出库明细');
  });
};
const getExportXls = async () => {
  loading.value = true;
  await onExportXls();
  loading.value = false;
};

const onChange = (_pagination, _filters, sorter) => {
  mySorter.column = sorter.field;
  if (sorter.order == "ascend") {
    mySorter.order = "asc";
  } else {
    mySorter.order = "desc";
  }
};


</script>

<style lang="scss" scoped>
.text_blue {
  color: #1890ff;
  cursor: pointer;
}

.foot {
  .foot-total {
    text-align: right;
  }
}

:v-deep(.ant-table-body) {
  overflow: hidden;
}
</style>
