import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getDepartListLeaf, getspdStoragelist } from '/@/api/common/api';
import { h } from 'vue';
import { Tag } from 'ant-design-vue'
import dayjs, { Dayjs } from 'dayjs';
const ranges = {
  今天: [dayjs().startOf("day"), dayjs()],
  明天: [
    dayjs().startOf("day"),
    dayjs().startOf("day").subtract(-1, "days"),
  ],
}
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '手术编号',
    align: 'center',
    dataIndex: 'applyPlaneCode',
    fixed: 'left',
    width: 120,
    resizable: true,
    sorter: true,
  },
  {
    title: '患者姓名',
    align: 'center',
    dataIndex: 'patientName',
    width: 100,
    resizable: true,
  },
  {
    title: '手术时间',
    align: 'center',
    dataIndex: 'operationTime',
    width: 120,
    resizable: true,
    sorter: true,
  },
  {
    title: '手术科室',
    align: 'center',
    dataIndex: 'departName',
    width: 120,
    resizable: true,
  },
  {
    title: '申领科室',
    align: 'center',
    dataIndex: 'requestDepartName',
    width: 120,
    resizable: true,
  },
  {
    title: '术间-台次',
    align: 'center',
    dataIndex: 'operationNum',
    width: 100,
    resizable: true,
  },
  {
    title: '发放状态',
    align: 'center',
    dataIndex: 'sendStatus_dictText',
    width: 120,
    resizable: true,
    sorter: true,
  },
  {
    title: '回收状态',
    align: 'center',
    dataIndex: 'recoveryStatus_dictText',
    width: 120,
    resizable: true,
    sorter: true,
  },
  {
    title: '计费状态',
    align: 'center',
    dataIndex: 'chargeStatus_dictText',
    width: 120,
    resizable: true,
    sorter: true,
    customRender: ({ text }) => {
      let str: string = text
      let color = ''
      if (text == '全部计费') {
        color = 'green'
      } else if (text == '部分计费') {
        color = "red"
      }
      return h(Tag, { color: color }, () => str);
    }
  },
  {
    title: '是否超时未计费',
    align: 'center',
    dataIndex: 'billFlag_dictText',
    width: 140,
    resizable: true,
    sorter: true,
  },
  {
    title: '响应库房',
    align: 'center',
    dataIndex: 'storageName',
    width: 130,
    resizable: true,
  },
  {
    title: '医生姓名',
    align: 'center',
    dataIndex: 'doctorName',
    width: 100,
    resizable: true,
  },
  {
    title: '代销出库状态',
    align: 'center',
    dataIndex: 'consignmentOutboundStatus_dictText',
    width: 100,
    resizable: true,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '手术日期',
    field: 'operationTime',
    component: 'RangePicker',
    componentProps: { valueType: 'Date', ranges: ranges }
  },
  {
    label: '手术科室',
    field: 'departId',
    component: 'ApiSelect',
    componentProps: {
      api: getDepartListLeaf,
      params: {},
      showSearch: true,
      optionFilterProp: 'departNameDepartNameAbbr',
      labelField: 'departName',
      valueField: 'id',
      allowClear: true,
    },
  },
  {
    label: '申领科室',
    field: 'requestDepartId',
    component: 'ApiSelect',
    componentProps: {
      api: getDepartListLeaf,
      params: {},
      showSearch: true,
      optionFilterProp: 'departNameDepartNameAbbr',
      labelField: 'departName',
      valueField: 'id',
      allowClear: true,
    },
  },
  {
    label: '响应库房',
    field: 'storageId',
    component: 'ApiSelect',
    componentProps: {
      api: () => getspdStoragelist({ column: 'storageType', order: 'asc' }),
      params: {},
      showSearch: true,
      optionFilterProp: 'storageNameAbbr',
      labelField: 'storageName',
      valueField: 'id',
    },
  },
  {
    label: '医生姓名',
    field: 'doctorName',
    component: 'Input',
  },
  {
    label: '患者姓名',
    field: 'patientName',
    component: 'JInput',
  },
  {
    label: '发放状态',
    field: 'sendStatusList',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'send_status',
        mode: 'multiple',
      };
    },
  },
  {
    label: '回收状态',
    field: 'recoveryStatusList',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'recovery_status',
        mode: 'multiple',
      };
    },
  },
  {
    label: '术间-台次',
    field: 'operationNum',
    component: 'Input',
  },
  {
    label: '手术编号',
    field: 'applyPlaneCode',
    component: 'JInput',
  },
  {
    label: '计费状态',
    field: 'chargeStatusList',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'charge_status',
        mode: 'multiple',
      };
    },

  }, {
    label: '代销出库状态',
    field: 'consignmentOutboundStatusList',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'operation_consignment_out_store_type',
        mode: 'multiple',
      };
    },
  },
];
