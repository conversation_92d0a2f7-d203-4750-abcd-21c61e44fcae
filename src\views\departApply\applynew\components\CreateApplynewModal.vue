<template>
  <div>
    <BasicModal
      v-bind="$attrs"
      @register="registerModal"
      :title="title"
      :defaultFullscreen="true"
      @ok="handleSubmit"
      destroy-on-close
      :maskClosable="false"
    >
      <a-row>
        <a-col :span="5">
          <span><strong style="color: red">*</strong>申领科室：</span>
          <a-select
            v-model:value="departSelected"
            placeholder="请选择申领科室"
            style="min-width: 150px"
            :disabled="isUpdate"
            @change="changeDepart"
            @click="clickDepart"
          >
            <template v-for="depart in departList" :key="depart.id">
              <a-select-option :value="depart.id">{{
                depart.departName
              }}</a-select-option>
            </template>
          </a-select>
        </a-col>
        <a-col :span="5">
          <span><strong style="color: red">*</strong>需求类型：</span>
          <JDictSelectTag
            type="select"
            v-model:value="requirementType"
            dictCode="apply_type_of_requirement"
            placeholder="请选择需求类型"
            :disabled="!showFooterFlg"
          />
        </a-col>
        <a-col :span="5">
          <span>送货时间：</span>
          <a-date-picker
            format="YYYY-MM-DD"
            valueFormat="YYYY-MM-DD"
            :disabled-date="disabledDate"
            v-model:value="requestDate"
            :disabled="!showFooterFlg"
          />
        </a-col>

        <a-col :span="5">
          <span>送货地址：</span>
          <a-input
            placeholder="请输入送货地址"
            v-model:value="address"
            style="width: 220px"
            :disabled="!showFooterFlg"
          ></a-input>
        </a-col>
        <a-col :span="4">
          <span>备注：</span>
          <a-input :disabled="showFooterFlg ? false : true" v-model:value="remark" style="width: 200px">
            <template #suffix>
              <a-popover v-model:visible="visible" trigger="click" placement="bottom">
                <template #content>
                  <a-textarea :disabled="!showFooterFlg" v-model:value="remark" placeholder="" show-count :maxlength="30" :rows="6" allow-clear/>
                </template>
                <fullscreen-outlined @click="visible = !visible" />
              </a-popover>
            </template>
          </a-input>
        </a-col>
      </a-row>
      <BasicTable
        id="saveTable"
        :action-column="showFooterFlg ? actionColumn : false"
        bordered
        size="middle"
        rowKey="id"
        :columns="showFooterFlg ? planColumns : detailplanColumns"
        :canResize="false"
        :dataSource="innerData"
        :pagination="false"
        :scroll="{ y: 586 }"
        @change="handleTableChange"
         @row-click="handleRowClick"
      >
        <template #tableTitle v-if="showFooterFlg">
          <a-button type="primary" @click="addDepartMaterial()">添加物资</a-button>
          <a-button type="primary" @click="addDepartPackage()">添加套包</a-button>
          <a-button type="primary" @click="addApplylist()">历史单导入</a-button>
          <a-button type="primary" @click="handleSum">单据汇总</a-button>
        </template>
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'goodsCode'">
            <a-input v-if="record.goodsId === '-1'" v-model:value="record.goodsCode" @change="handaleChangeInp(column, text, record)"     @keydown.enter="clickEnterFunction" />
            <span v-else>{{ record.goodsCode }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'goodsName'">
            <a-input  v-if="record.goodsId === '-1'" v-model:value="record.goodsName" @change="handaleChangeInp(column, text, record)"    @keydown.enter="clickEnterFunction"/>
            <span v-else>{{ record.goodsName }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'goodsSpecs'">
            <a-input  v-if="record.goodsId === '-1'" v-model:value="record.goodsSpecs" @change="handaleChangeInp(column, text, record)"    @keydown.enter="clickEnterFunction"/>
            <span v-else>{{ record.goodsSpecs }}</span>
          </template>
        </template>
        <template #applyNum="{ record }">
          <a-input v-if="record.goodsId !== '-1' && record.quantitativePackageFlag == 1" @blur="handleBlur(record,$event)" @input="handleInp($event,record)" v-model:value="record.applyNum" placeholder="请输入数量"/>
          <a-input v-if="record.goodsId !== '-1' && record.quantitativePackageFlag != 1" @input="handleInp($event,record)" v-model:value="record.applyNum" placeholder="请输入数量"/>
        </template>
        <template #goodsRemark="{record}">
          <a-input :disabled="showFooterFlg ? false : true" v-model:value="record.goodsRemark" v-if="record.goodsId != '-1'">
            <template #suffix>
              <a-popover v-model:visible="record.visible" trigger="click" placement="right">
                <template #content>
                  <a-textarea :disabled="showFooterFlg ? false : true" v-model:value="record.goodsRemark" placeholder="" :rows="8" allow-clear/>
                </template>
                <fullscreen-outlined />
              </a-popover>
            </template>
          </a-input>
        </template>
        <!-- 定数包规格 -->
        <template #quantitativePackageList="{ record }">
          <a-select :disabled="isDisabled" v-if="record.quantitativePackageFlag == 1"  @change="handleSelect(record, $event)" style="width: 200;" v-model:value="record.quantitativePackageId" 
            :options="record.packageList">
          </a-select>
        </template>
        <!-- 定数包数量 -->
        <template #packageNum="{ record }">
          <span v-if="record.quantitativePackageFlag == 1">{{record.packageNum }}</span>
        </template>
        <template #goodsSpecsDetail="{ record }">
          <!-- options展示列表  readOnly 不可编辑 -->
          <a-select v-if="record.goodsSpecsDetailOperationType === 'options'" style="width: 220px;" v-model:value="record.goodsSpecsDetail" 
            :options="record.goodsSpecsDetailOptions">
          </a-select>
          <span v-else>{{ record.goodsSpecsDetail }}</span>
        </template>
        <template #totalAmt="{ record }">
          <span v-if="record.goodsId !== '-1'">{{ (record.applyNum * record.goodsPrice).toFixed(2) }}</span>
        </template>
        <template #action="{ record }">
          <TableAction
            :actions="[
              {
                label: '',
                icon: 'ion:arrow-up-outline',
                ifShow:record.goodsId !== '-1',
                onClick: rowUp.bind(null, record),
              },
              {
                label: '',
                icon: 'ion:arrow-down-outline',
                ifShow:record.goodsId !== '-1',
                onClick: rowDown.bind(null, record),
              },
              {
                label: '删除',
                icon: 'ic:outline-delete-outline',
                ifShow:record.goodsId !== '-1',
                onClick: handleDel.bind(null, record),
              },
            ]"
          />
        </template>
        <template #footer="currentPageData">
          <div class="foot pd6 dpflex jcsb">
            <div>合计</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
          </div>
        </template>
      </BasicTable>
      <div
        class="divfloat"
        :class="searchFlg ? 'boxahadow' : 'boxnoshow'"
         :style="getstyle"
        ref="divfloat"
        @mousedown="mousedownFunction"
      >
        <BasicTable
          :loading="loading"
          v-show="searchFlg"
          :action-column="actionSearchColumn"
          bordered
          size="small"
          rowKey="goodsId"
          :canResize="false"
          :columns="searchColumns"
          :dataSource="searchData"
          :pagination="false"
          :scroll="{ y: 300 }"
          :ellipsis="true"
          :showActionColumn="false"
          @row-click="handleSearchRowClick"
        >
          <!--<template #action="{ record }">
            <TableAction
              :actions="[
                {
                  label: '添加',
                  icon: 'ic:outline-delete-outline',
                  onClick: handleAdd.bind(null, record),
                },
              ]"
            />
          </template>-->
          <template #footer>
            <div class="page" style="text-align: right">
              <Pagination
                @change="changePage"
                @showSizeChange="changeSize"
                :defaultPageSize="5"
                v-model:current="page.pageNo"
                v-model:pageSize="page.pageSize"
                size="small"
                :total="total"
                :pageSizeOptions="['10', '50', '100', '500']"
                :show-total="(total) => `共 ${total} 条数据`"
                show-size-changer
                show-quick-jumper
              />
            </div>
          </template>
        </BasicTable>
      </div>
    </BasicModal>
    <SelectDepartMaterial
      @register="regModal"
      @returndata="setMaterialValue"
    ></SelectDepartMaterial>
    <SelectPackage
      @register="regModalSelectPackage"
      @returndata="setPacksgeValue"
    ></SelectPackage>
    <SelectApplylist
      @register="regModalSelectApplylist"
      @returndata="setApplylistValue"
    ></SelectApplylist>
  </div>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from "/@/components/Modal";
import { useModal } from "/@/components/Modal";
import SelectDepartMaterial from "/@/components/Form/src/jeecg/components/modal/SelectDepartMaterial.vue";
import SelectPackage from "./SelectPackage.vue";
import SelectApplylist from "./SelectApplylist.vue";
import { ref, computed, unref, onMounted, nextTick, reactive } from "vue";
import { BasicTable, TableAction } from "/@/components/Table";
import { Pagination } from "ant-design-vue";
import { getNumToThousands } from '/@/utils/index';
import { FullscreenOutlined } from '@ant-design/icons-vue';
import {
  addApplyOrder,
  getSpdApplyOrderDetailllist,
  editApplyOrder,
  detailSummary
} from "./../Applynew.api";
import { planColumns, searchColumns, detailplanColumns } from "./../Applynew.data";
import { useMessage } from "/@/hooks/web/useMessage";
import moment from "moment";
import JDictSelectTag from "/@/components/Form/src/jeecg/components/JDictSelectTag.vue";
import { queryGoodsBydepart, queryMyDeptListByTenantId } from "/@/api/common/api";
import { useUserStore } from "/@/store/modules/user";
import { useDraggable } from '/@/spdHooks/useDraggable'
const { dragElement:divfloat,mousedownFunction } = useDraggable();
const userStore = useUserStore();
const { createConfirm } = useMessage();
import { useDebounceFn } from "@vueuse/core";
const getFootTotal = (currentPageData) => `${handleSummary(currentPageData).applyNum} ,\xa0` + `${handleSummary(currentPageData).totalAmt}`
//定义响应式变量
let title = ref("");
const requestDate = ref("");
const departSelected = ref();
const tempdepartSelected = ref();
const requirementType = ref("");
const address = ref("");
const remark = ref(""); //备注
const visible = ref<boolean>(false);
const innerData = ref<any[]>([]);
const departList = ref([]);
const isUpdate = ref(true);
const showFooterFlg = ref(true);
const isDisabled = ref() //是否禁用
const id = ref("");

const searchData = ref<any[]>([]);
const searchFlg = ref(false);
const pageY = ref(0);
const loading = ref(false);
// 分页搜索传参
const page = reactive<any>({
  pageNo: 1,
  pageSize:5,
  queryDepartLimit:'1',
  goodsCode: undefined,
  goodsName__goodsSimpleCode__goodsCommonName_orFlag: undefined,
  goodsSpecs: undefined,
});
const reset = () => {
  page.goodsName__goodsSimpleCode__goodsCommonName_orFlag = undefined;
  page.goodsCode = undefined;
  page.goodsName = undefined;
  page.goodsSpecs = undefined;
};
const total = ref(0);
const getList = () => {
  page.departId = departSelected.value;
  loading.value = true;
  queryGoodsBydepart(page).then((res) => {
    total.value = res.total;
    searchData.value = res.records;
    loading.value = false;
  });
};
const changePage = (pag, pageSize) => {
  page.pageNo = pag;
  getList();
};
const changeSize = (current, size) => {
  page.pageNo = 1;
  page.pageSize = size;
  getList();
};
const emit = defineEmits(["success"]);
const { createMessage } = useMessage();

const actionColumn = {
  width: 140,
  title: "操作",
  dataIndex: "action",
  slots: { customRender: "action" },
  fixed: "right",
};
const actionSearchColumn = {
  width: 60,
  title: "操作",
  dataIndex: "action",
  slots: { customRender: "action" },
  fixed: "right",
};
function handleSummary(tableData: Recordable[]) {
  // 金额合计
  const list = tableData.filter(v => v.goodsId != '-1')
  const totalAmount = list.reduce((prev, next) => {
    prev += Number(next.totalAmt);
    return prev;
  }, 0);
  // 数量总计
  const totalNumber = list.reduce((prev, next) => {
    prev += Number(next.applyNum ? next.applyNum : 0);
    return prev;
  }, 0)
  return {
    totalAmt: `当前总金额 : ${getNumToThousands(totalAmount)}`,
    applyNum: `当前总数量 : ${totalNumber.toFixed(2)}`,
  }
}
//注册model
const [regModal, { openModal }] = useModal();
const [regModalSelectPackage, { openModal: openSelectPackage }] = useModal();
const [regModalSelectApplylist, { openModal: openSelectApplylist }] = useModal();

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  reset();
  setModalProps({
    confirmLoading: false,
    showCancelBtn: !!data?.showFooter,
    showOkBtn: !!data?.showFooter,
    okText: "保存申领单",
  });
  isUpdate.value = !!data?.isUpdate;
  showFooterFlg.value = data?.showFooter;
  innerData.value = [];
  loadDepartList();
  searchFlg.value = false;
  isDisabled.value = data.isDisabled
  if (unref(isUpdate)) { //编辑
    const result = await getSpdApplyOrderDetailllist({
      parentApplyOrderId: data.record.id,
    });
    id.value = data.record.id;
    departSelected.value = data.record.departId;
    requirementType.value = data.record.typeOfRequirement + "";
    requestDate.value = data.record.requestDate;
    address.value = data.record.deliveryAddress;
    remark.value = data.record.remark;
    innerData.value = result
    innerData.value.forEach((v,i)=>{
      v.index = i
      if(Array.isArray(v.quantitativePackageList) === true){
        v.packageList = v.quantitativePackageList.map(item=>{
          return{
            label: item.quantitativePackageSpecs,
            value: item.id,
            quantitativePackageNum: item.quantitativePackageNum,
          }
        })
      }
    })
    // result.forEach((item) => {
    //   innerData.value.push(item);
    // });
  } else {
    departSelected.value = userStore.hospitalZoneInfo.depart.id;
    requirementType.value = "1";
    address.value = "";
    remark.value = "";
  }
  if (!showFooterFlg.value) {
    title.value = "查看申领单";
  } else {
    title.value = !unref(isUpdate) ? "新增申领单" : "编辑申领单";
    innerData.value.push({ goodsId: '-1' });
  }
});
//打开添加物资页面
function addDepartMaterial() {
  if (departSelected.value == undefined || departSelected.value == "") {
    createMessage.warning("请先选择申领科室！");
    return;
  } else {
    openModal(true, {
      departId: departSelected.value,
    });
  }
}
//打开添加套包页面
function addDepartPackage() {
  if (departSelected.value == undefined || departSelected.value == "") {
    createMessage.warning("请先选择申领科室！");
    return;
  } else {
    openSelectPackage(true, {
      departId: departSelected.value,
    });
  }
}
//打开历史单导入页面
function addApplylist() {
  if (departSelected.value == undefined || departSelected.value == "") {
    createMessage.warning("请先选择申领科室！");
    return;
  } else {
    openSelectApplylist(true, {
      departId: departSelected.value,
    });
  }
}
//获取采购部门
async function loadDepartList() {
  const result = await queryMyDeptListByTenantId({delFlag:0});
  if (!result || result.length == 0) {
    return;
  }
  departList.value = result;
}
/**
 * 返回勾选物料数据
 */
function setMaterialValue(data) {
  setData(data, 0);
}
/**
 * 返回勾选套包数据
 */
function setPacksgeValue(data) {
  setData(data, 0);
}
/**
 * 返回勾选历史单数据
 */
function setApplylistValue(data) {
  setData(data, 1);
}
//统一设置需要添加的数据
function setData(data, type) {
  innerData.value.pop();
  data.forEach((item,index,arr) => {
    // 处理规格型号
    if(item.goodsSpecsDetailOperationType === 'options'){
      item.goodsSpecsDetailOptions.forEach(( v, i , arr )=>{
        arr[i] = {
          label: v,
          value: v 
        };
      })
    }
    // 处理定数包规格数量
    if(item.quantitativePackageFlag == 1){
      item.packageList = item.quantitativePackageList.map(( v )=>{
        return{
          label: v.quantitativePackageSpecs,
          value: v.id,
          quantitativePackageNum: v.quantitativePackageNum,
        }
      })
      item.quantitativePackageId = item.packageList[0]?.value
      item.quantitativePackageNum = item.packageList[0]?.quantitativePackageNum //数量赋值
      //处理物资定数包
      item.packageNum = Math.ceil(Number(item.applyNum) / Number(item.quantitativePackageNum));
      item.applyNum = Number(item.packageNum) * Number(item.quantitativePackageNum) ;
    }
    // if(type === 0 ){
    if (item.maxApplyNum !== null) {
      if (Number(item.applyNum) > Number(item.maxApplyNum)) {
        item.applyNum = null;
        item.packageNum = null;
      }
    }
    innerData.value.push(item);
    // }
  });

  innerData.value.forEach((v,i)=>{
    v.index = i
  })
  innerData.value.push({ goodsId: '-1' });
  searchData.value = [];
  searchFlg.value = false;
}
//单据汇总
const handleSum = async () => {
  const len = innerData.value.length
  if(len == 1 ) return createMessage.error("当前汇总数据为空");
  let saveData = innerData.value.filter(v=>v.goodsId !== '-1').map(item=>{
    return{
      "applyNum": item.applyNum,
      "expensiveFlag": item.expensiveFlag,
      "goodsCode": item.goodsCode,
      "goodsId": item.goodsId,
      "goodsName": item.goodsName,
      "goodsPrice": item.goodsPrice,
      "goodsSpecs": item.goodsSpecs,
      "goodsSpecsDetail":item.goodsSpecsDetail,
      "goodsType": item.goodsType,
      "manufacturerId": item.manufacturerId,
      "manufacturerName": item.manufacturerName,
      "outStorageId": item.outStorageId,
      "outStorageName": item.outStorageName,
      "packageNum": item.applyNum / parseInt(item.quantitativePackageNum),
      "purchaseAttribute": item.purchaseAttribute,
      "quantitativePackageFlag": item.quantitativePackageFlag,
      "quantitativePackageId": item.quantitativePackageId,
      "quantitativePackageNum": item.quantitativePackageNum,
      "registerNo": item.registerNo,
      "supplierId": item.supplierId,
      "supplierName": item.supplierName,
      "totalAmt": item.totalAmt,
      "unitName": item.unitName,
      "goodsRemark": item.goodsRemark,
    }
  });
  const res = await detailSummary(saveData)
  innerData.value = res
  innerData.value.forEach((v,i)=>{
    v.index = i
    if(Array.isArray(v.quantitativePackageList) === true){
      v.packageList = v.quantitativePackageList.map(item=>{
        return{
            label: item.quantitativePackageSpecs,
            value: item.id,
            quantitativePackageNum: item.quantitativePackageNum,
          }
      })
    }
  })
  innerData.value.push({ goodsId: '-1' });
}
//表单提交事件
async function handleSubmit() {
  try {
    let departs = departList.value.filter((o) => o.id == departSelected.value);
    let saveData = innerData.value.filter(v=>v.goodsId !== '-1').map(item=>{
      return{
        "applyNum": item.applyNum,
        "expensiveFlag": item.expensiveFlag,
        "goodsCode": item.goodsCode,
        "goodsId": item.goodsId,
        "goodsName": item.goodsName,
        "goodsPrice": item.goodsPrice,
        "goodsSpecs": item.goodsSpecs,
        "goodsSpecsDetail":item.goodsSpecsDetail,
        "goodsType": item.goodsType,
        "manufacturerId": item.manufacturerId,
        "manufacturerName": item.manufacturerName,
        "outStorageId": item.outStorageId,
        "outStorageName": item.outStorageName,
        "packageNum": item.applyNum / parseInt(item.quantitativePackageNum),
        "purchaseAttribute": item.purchaseAttribute,
        "quantitativePackageFlag": item.quantitativePackageFlag,
        "quantitativePackageId": item.quantitativePackageId,
        "quantitativePackageNum": item.quantitativePackageNum,
        "registerNo": item.registerNo,
        "supplierId": item.supplierId,
        "supplierName": item.supplierName,
        "totalAmt": item.totalAmt,
        "unitName": item.unitName,
        "goodsRemark": item.goodsRemark,
      }
    });
    // console.log(saveData,'saveData');
    let requestDatestr = requestDate.value;
    if (requestDatestr != null && requestDatestr != "") {
      requestDatestr = requestDate.value + " 00:00:00";
    }
    let values = {
      applyDetails: saveData,
      departId: departSelected.value,
      departName: departs[0].departName,
      deliveryAddress: address.value,
      requestDate: requestDatestr,
      remark: remark.value,
      typeOfRequirement: requirementType.value,
    };
    // console.log(values,'values');
    if (unref(isUpdate)) {
      values["applyOrderId"] = id.value;
      setModalProps({ confirmLoading: true });
      //编辑表单
      await editApplyOrder(values);
    } else {
      setModalProps({ confirmLoading: true });
      //新建表单
      await addApplyOrder(values);
    }

    //关闭弹窗
    closeModal();
    //刷新列表
    emit("success");
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
/**
 * 删除事件
 */
function handleDel(record: Recordable) {
  const index = innerData.value.findIndex((item) => item.index === record.index);
  innerData.value.splice(index, 1);
}
const getstyle = computed(() => {
  if (pageY.value > 500) {
    return { top: pageY.value-530 + "px" };
  } else {
    return { top: -30 + pageY.value + "px" };
  }
});
//延时搜索
const queryData = useDebounceFn(queryDataByparams, 300);
async function queryDataByparams(value) {
  page.goodsName__goodsSimpleCode__goodsCommonName__goodsCode__goodsSpecs_orFlag = value ? `*${value}*` :undefined
  loading.value = true;
  page.departId = departSelected.value;
  page.pageSize =5;
  page.pageNo = 1;
  page.tempPurchaseFlag =0;
  const result = await queryGoodsBydepart(page);
  total.value = result.total;
  searchData.value = result.records;
  loading.value = false;
  searchFlg.value = true;
}
/**
 * 添加检索到的数据
 */
function clickEnterFunction() {
  if(  searchFlg.value ==true&& searchData.value.length > 0 ){
    addRow(searchData.value[0]);
  }
}

function handleSearchRowClick(record, index, event) {
  addRow(record);
}

// 重置表格搜索数据
function resetSearchData(innerData) {
  const length = innerData.value.length - 1
  innerData.value.forEach(()=>{
    innerData.value[length].goodsCode = undefined
    innerData.value[length].goodsName = undefined
    innerData.value[length].goodsSpecs = undefined
  })
}
function addRow(record) {
    // 处理型号下拉数据格式
    if(record.goodsSpecsDetailOperationType === 'options'){
      record.goodsSpecsDetailOptions.forEach(( v, i , arr )=>{
        arr[i] = {
          label: v,
          value: v 
        };
      })
    }
    // 定数包规格处理
    if(record.quantitativePackageFlag == 1){
      record.packageList = record.quantitativePackageList.map(( v )=>{
        return{
          label: v.quantitativePackageSpecs,
          value: v.id,
          quantitativePackageNum: v.quantitativePackageNum,
        }
      })
      record.quantitativePackageId = record.packageList[0]?.value
      record.quantitativePackageNum = record.packageList[0]?.quantitativePackageNum //数量赋值
    }
    //新增行数据
    // let obj = innerData.value.filter((o) => o.goodsId == record.goodsId);
    // if (obj && obj.length > 0) {
    //   createMessage.warning(record.goodsName + "已经存在，不能继续添加");
    // } else {
      innerData.value.splice(-1, 0, record);
      let len =  innerData.value.length - 2;
      record.index = len;
      record.applyNum = "";
      record.totalAmt = "0";
      //添加物资后，数量获得焦点
      nextTick(() => {
        let div = window.document.querySelector("#saveTable");
        let tbody = div?.querySelector(".ant-table-tbody");
        let trlist = tbody?.getElementsByClassName("ant-table-row");
        (trlist?.[trlist.length - 2].getElementsByClassName(
          "ant-input"
        )[0] as HTMLElement).focus();
      });
    // }
    resetSearchData(innerData);
    reset();
    innerData.value.pop();
    innerData.value.push({goodsId:'-1'});
  // }
  searchData.value = [];
  searchFlg.value = false;
}
const handaleChangeInp = (column, text, record)=>{
  let value
  switch (column.dataIndex) {
    case 'goodsCode':value = record.goodsCode
      break;
    case 'goodsName':value = record.goodsName
      break;
    case 'goodsSpecs':value = record.goodsSpecs
      break;
    default:
      break;
  }
  if(!value) return searchFlg.value = false
  queryData(value)
}
const handleInp = (e,record)=>{
  record.applyNum = record.applyNum.replace(/[^0-9.]/g, '');
  const num = record.applyNum.split('.');
  if (num.length > 1) {
    num[1] = num[1].slice(0, 3);
    record.applyNum = num.join('.');
    record.applyNum = record.applyNum.replace(/(\.\d*?[1-9])0+$/, '$1');
  }
  if( record.maxApplyNum > 0 && Number(record.applyNum) > Number(record.maxApplyNum) ){
    createMessage.error("申领数量不能大于最大申领数量");
    record.applyNum = null
  }
  record.totalAmt = (record.applyNum * record.goodsPrice).toFixed(2) 
  record.packageNum = record.applyNum / parseInt(record.quantitativePackageNum)
}
  //倍数检测
const handleBlur = (record,event) => {
  if(!record.applyNum) return createMessage.error(`请输入申领数量`);
  if (parseFloat(record.applyNum) % parseInt(record.quantitativePackageNum) !== 0) {
    record.packageNum = Math.ceil(Number(record.applyNum) / Number(record.quantitativePackageNum));
    record.applyNum = Number(record.packageNum) * Number(record.quantitativePackageNum) ;
    record.totalAmt = (Number(record.applyNum) * Number(record.goodsPrice)).toFixed(2) ;
  }
}
// 定数包规格选择数量处理
const handleSelect = (record, $event)=>{
  record.quantitativePackageNum = record.quantitativePackageList.find( v => v.id == $event)?.quantitativePackageNum
  record.applyNum = ''
  record.packageNum = ''
}
function disabledDate(current) {
  return current < moment().subtract(1, "day");
}

function rowUp(record: Recordable) {
  const index = innerData.value.findIndex((item) => item.goodsId === record.goodsId);
  if (index == 0) {
    createMessage.warning("已是第一条数据，不能移动.");
    return;
  }
  if (index != 0) {
    innerData.value[index] = innerData.value.splice(
      index - 1,
      1,
      innerData.value[index]
    )[0];
  } else {
    innerData.value.splice(-1, 0, innerData.value.shift());
  }
}
function rowDown(record: Recordable) {
  const index = innerData.value.findIndex((item) => item.goodsId === record.goodsId);
  if (index == innerData.value.length - 2) {
    createMessage.warning("已是最后一条数据，不能移动.");
    return;
  }
  if (index != innerData.value.length - 2) {
    innerData.value[index] = innerData.value.splice(
      index + 1,
      1,
      innerData.value[index]
    )[0];
  } else {
    innerData.value.unshift(innerData.value.splice(index, 1)[0]);
  }
}
const handleTableChange = (pagination, filters, sorter) => {
  if (sorter.order) {
    innerData.value = innerData.value.sort((a, b) => {
      const aValue = a[sorter.field] || '';
      const bValue = b[sorter.field] || '';
      if (sorter.order === 'ascend') {
        return aValue.localeCompare(bValue, 'zh-Hans-CN', { numeric: true, ignorePunctuation: true });
      } else if (sorter.order === 'descend') {
        return bValue.localeCompare(aValue, 'zh-Hans-CN', { numeric: true, ignorePunctuation: true });
      }
      return 0;
    });
  }
}

function handleRowClick(record, _index, event) {
  pageY.value = event.y;
}
function changeDepart() {
  if (innerData.value.length == 1) return;
  createConfirm({
    iconType: "warning",
    title: "确认切换申领科室",
    content: "切换申领科室，添加的物资将会被清空！",
    okText: "确认",
    cancelText: "取消",
    onOk: () => {
      innerData.value = [];
      innerData.value.push({ goodsId: '-1' });
    },
    onCancel: () => {
      departSelected.value = tempdepartSelected.value;
    },
  });
}
function clickDepart() {
  tempdepartSelected.value = departSelected.value;
}
onMounted(() => {
  window.addEventListener("click", handleMousedown); //监听鼠标按下
});

function handleMousedown(event) {
  if (event.target.parentNode.className != "scrollbar__view") return;
  page.pageNo = 1;
  page.pageSize = 5;
  searchFlg.value = false;
}
</script>
<style lang="less" scoped>
.divfloat {
  position: absolute;
  top:230px;
  //left: 230px;
  width: 98%;
  background:#fff;
  box-shadow: 1px 1px 6px 0px black;
  z-index: 9999;
}
:deep(.ant-picker) {
  width:auto !important;
}
.boxahadow {
  display: block;
  box-shadow: 1px 1px 6px 0px black;
}
.boxnoshow {
  display: none;
  // box-shadow: 1px 1px 6px 0px black;
}
.ant-input-textarea-show-count::after{
  float: none;
}
</style>
