<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <!--插槽:table标题-->
      <template #tableTitle>
        <div class="btns">
          <a-button type="primary" @click="submits('push')">推送</a-button>
          <a-button type="primary" @click="submits('audit')">审核通过</a-button>
          <a-button type="primary" v-auth="'user:return'" @click="submits('return')">退回</a-button>
          <a-button type="primary" @click="print">打印</a-button>
        </div>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="dpflex">
            <div class="ml100">{{ `${checkSumAmount}` }}</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </div>
    </template>
      <template #invoiceNo="{record}">{{ record.invoiceNo?record.invoiceNo.split(',')[0]:record.invoiceNo }}</template>
    </BasicTable>
    <div class="wrapper" ref="wrapper">
      <!-- 发票号modal -->
      <invoice-num-modal v-bind="$attrs" @register="InvoiceModal"></invoice-num-modal>
      <!-- 结算单号modal -->
      <settle-num-modal v-bind="$attrs" @register="SettleModal"></settle-num-modal>
    </div>
    <settle-print :list="printList" ref="settlePrintRef"></settle-print>
  </div>
</template>

<script lang="ts" setup name="settlement-seetleOrderMange">
import { ref, computed } from 'vue'
import { BasicTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage'
import { columns, searchFormSchema } from './index.data'
import { useModal } from '/@/components/Modal';
import { list, auditBatch, pushBatch, listPrint } from './index.api';
import InvoiceNumModal from './components/InvoiceNumModal.vue'
import SettleNumModal from './components/SettleNumModal.vue'
import { useMessage } from '/@/hooks/web/useMessage';
import { handleSummaryNew, checkedSum } from '/@/utils/index'
const getFootTotal = (currentPageData)=> handleSummaryNew(currentPageData,'totalAmt','totalNum')
const checkSumAmount = computed(() => checkedSum(selectedRows.value,'totalAmt','totalNum')) // 勾选合计
const { createMessage } = useMessage()
const printList = ref<any>([])
const settlePrintRef = ref()
//注册model
const [InvoiceModal, { openModal: InvoiceOpenModal }] = useModal(); // 发票号modal
const [SettleModal, { openModal: SettleOpenModal }] = useModal(); //  结算单号modal
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll:{y:480},
    showActionColumn: true,
    formConfig: {
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      labelCol: { xxl:8 },
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
        ['settlementBillDate', ['settlementBillDate_begin', 'settlementBillDate_end'], 'YYYY-MM-DD'],
        ['createTime', ['createTime_begin', 'createTime_end'], 'YYYY-MM-DD'],
        ['auditDate', ['auditDate_begin', 'auditDate_end'], 'YYYY-MM-DD'],
      ],
    },
    actionColumn: {
      width: 200,
      fixed: 'right'
    },
  },
})
const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext
const success = (): void => {
  (selectedRowKeys.value = []) && reload();
}
const pushStrategy = async (params, success) => {
  const isPush = selectedRows.value.some(item => item.pushStatus == 1)
  if (isPush) return createMessage.warning("包含已推送的结算单,无重复推送,请重新选择!");
  const isAudit = selectedRows.value.some(item => item.auditStatus == 0)
  if (isAudit) return createMessage.warning("包含待审核的结算单,请先审核!");
  delete params.auditStatus;
  await pushBatch(params, success);
};
const auditStrategy = async (params, success) => {
  params.auditStatus = 1;
  await auditBatch(params, success, params.auditStatus);
};
const returnStrategy = async (params, success) => {
  params.auditStatus = 3;
  await auditBatch(params, success, params.auditStatus);
};
const strategies = {
  push: pushStrategy,
  audit: auditStrategy,
  return: returnStrategy
};
const submits = async (type) => {
  const selectedRowKeysLength = selectedRowKeys.value.length;
  if (selectedRowKeysLength === 0) {
    return createMessage.warning("请先选择要处理的数据！");
  }
  let params = <any>{
    auditStatus: undefined,
    ids: selectedRowKeys.value
  };
  if (strategies[type]) {
    await strategies[type](params, success);
  } else {
    console.error(type);
  }
};
const print = async () => {
  const selectedRowKeysLength = selectedRowKeys.value.length;
  if (selectedRowKeysLength === 0) {
    return createMessage.warning("请先选择要处理的数据！");
  }
  let params = <any>{
    size: 6,
    ids: [],
  }
  params.ids = selectedRowKeys.value;
  const res = await listPrint(params)
  // console.log(res, 'res');
  printList.value = res
  // console.log(printList.value);
  setTimeout(function () {
    settlePrintRef.value.print();
  }, 100);
  return
}
/**
 * 查看
*/
const handleDetail = (record: Recordable) => {
  console.log('查看', record);
  InvoiceOpenModal(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
}
/**
   * 操作栏
   */
const getTableAction = (record) => {
  return [
    {
      label: '结算单详情',
      onClick: settleNum.bind(null, record),
    },
    {
      label: '发票查看',
      onClick: handleDetail.bind(null, record),
    },
  ]
}
/**
 * 结算单号事件
 */
const settleNum = (record) => {
  SettleOpenModal(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
}
</script>

<style lang="scss" scoped>
.btns {
  button {
    margin: 0 0 0 20px;
  }
}

:deep(.ant-calendar-picker) {
  width: 100%;
}

:deep(.ant-modal-content) {
  .ant-modal-close-x .jeecg-basic-modal-close {
    color: #fff !important;

    & .anticon:hover {
      color: #ed6f6f;
    }
  }

  .ant-modal-header {
    background-color: #1a8efe !important;

    .ant-modal-title .jeecg-basic-title {
      color: #fff !important;
    }
  }
}

// ::v-deep .ant-table-body {
//   height: unset !important;
// }
</style>