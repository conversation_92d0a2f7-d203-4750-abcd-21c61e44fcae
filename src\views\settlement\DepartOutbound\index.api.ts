import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/spd/spdOutstore/customizePageList',
  listDetail = '/spd/spdOutstoreDetail/list',
  listDetailRecord = '/spd/spdOutstoreDetailRecord/list',
  revoke = '/spd/spdOutstore/revoke',

  lowlList = '/spd/spdInventory/inventoryDetailsQuery',
  addByBatchNo = '/spd/spdOutstore/addByBatchNo',
  inventoryPackageList='/spd/spdInventory/inventoryPackageList',
  inventoryDetailPageList='/spd/spdInventory/inventoryDetailPageList',
}
export const list = (params) => defHttp.get({ url: Api.list, params });
export const listDetail = (params) => defHttp.get({ url: Api.listDetail, params });
export const listDetailRecord = (params) => defHttp.get({ url: Api.listDetailRecord, params });

export const lowlList = (params) => defHttp.get({ url: Api.lowlList, params });
export const addByBatchNo = (data) => defHttp.post({ url: Api.addByBatchNo, data });
export const inventoryPackageList = (data) => defHttp.get({ url: Api.inventoryPackageList, params: data });
export const inventoryDetailPageList =async (data) => await defHttp.get({ url: Api.inventoryDetailPageList, params: data });
export const revoke =async (data) => await defHttp.post({ url: Api.revoke, params: data });