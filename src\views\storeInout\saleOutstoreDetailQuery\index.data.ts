import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getspdStoragelist, suppliernoPagelist } from '/@/api/common/api';
import { FormatNumToThousands, getStartToEndDate } from '/@/utils/index'
import { useUserStore } from '/@/store/modules/user';
const { hospitalZoneInfo } = useUserStore();
import { usePermission } from '/@/hooks/web/usePermission';
const { hasPermission } = usePermission();
//查询数据
export const Schema: FormSchema[] = [
  {
    field: 'createTime',
    component: 'RangePicker',
    label: '制单日期',
    defaultValue: getStartToEndDate(),
    required: true,
    componentProps: { valueType: 'Date' },
  },
  {
    label: '执行科室',
    field: 'applyDepartId',
    component: 'ApiSelect',
    slot: 'depart',
    defaultValue: hasPermission('saleOutstoreDetailQuery:depart') ? hospitalZoneInfo?.depart.id : undefined,
  },
  {
    label: '业务类型',
    field: 'consignmentType',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'settlementbills_type',
      };
    },
  },
  { field: 'consignmentNo', component: 'JInput', label: '代销出库单号', },
  { field: 'goodsName__goodsCode__goodsSimpleCode_orFlag', component: 'JInput', label: '物资名称', },
  {
    label: '出库仓库',
    field: 'outStorageId',
    component: 'ApiSelect',
  },
  {
    label: '供应商',
    field: 'supplierId',
    component: 'ApiSelect',
    componentProps: {
      api: suppliernoPagelist,
      labelField: 'supplierName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: "supplierNameSupplyPycode",
    },
  },
  { field: 'manufacturerName', component: 'JInput', label: '生产厂商', },
  {
    label: '制单人',
    field: 'outUserName',
    component: 'JInput',
    slot: 'outUser'
  },
  { field: 'barCode', component: 'JInput', label: '耗材条码', },
  { field: 'udiCode', component: 'JInput', label: 'UDI', },
  { field: 'goodsSpecs', component: 'JInput', label: '规格', },
  { field: 'patientName', component: 'JInput', label: '病人姓名', },
  {
    field: 'bulkPurchaseTypeCode',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'bulk_purchase_type',
      };
    }, label: '带量标识',
  },
  { field: 'registerNo', component: 'JInput', label: '注册证号', },
  {
    label: '财务分类',
    field: 'financeCategory',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'db_finance_category',
      };
    },
  },
  {
    label: '资金来源',
    field: 'fundSource',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'fund_source',
      };
    },
  },
  {
    label: '响应仓库',
    field: 'responseStorageId',
    // required: true,
    colProps: {
      span: 4,
    },
    component: 'ApiSelect',
    componentProps: {
      api: getspdStoragelist,
      params: {
        //parentId:userStore.getHospitalZoneInfo.storage.id
      },
      showSearch: true,
      optionFilterProp: 'storageNameAbbr',
      labelField: 'storageName',
      valueField: 'id',
    },
  },
]
export const columns: BasicColumn[] = [
  {
    title: '出库单号',
    align: 'center',
    dataIndex: 'consignmentNo',
    width: 200,
    fixed: 'left',
    slots: { customRender: 'consignmentNo' },
    resizable: true,
  },
  {
    title: '制单日期',
    align: 'center',
    dataIndex: 'createTime',
    width: 160,
    resizable: true,
  },
  {
    title: '响应仓库',
    align: 'center',
    dataIndex: 'responseStorageName',
    width: 120,
    resizable: true,
  },
  {
    title: '出库仓库',
    align: 'center',
    dataIndex: 'outStorageName',
    width: 120,
    resizable: true,
  },
  {
    title: '执行科室',
    align: 'center',
    dataIndex: 'applyDepartName',
    width: 160,
    resizable: true,
  },
  {
    title: '物资分类',
    align: 'center',
    dataIndex: 'goodsCategory',
    width: 120,
    resizable: true,
  },
  {
    title: '财务分类',
    align: 'center',
    dataIndex: 'financeCategory_dictText',
    width: 120,
    resizable: true,
  },
  {
    title: '资金来源',
    align: 'center',
    dataIndex: 'fundSource_dictText',
    width: 120,
    resizable: true,
  },
  {
    title: '物资编码',
    align: 'center',
    dataIndex: 'goodsCode',
    width: 150,
    resizable: true,
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 180,
    resizable: true,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width: 150,
    resizable: true,
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width: 120,
    resizable: true,
  },
  {
    title: '带量标识',
    align: 'center',
    dataIndex: 'bulkPurchaseType_dictText',
    width: 120,
    resizable: true,
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unitName',
    width: 80,
    resizable: true,
  },
  {
    title: '数量',
    align: 'center',
    dataIndex: 'goodsNum',
    width: 80,
    resizable: true,
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'goodsPrice',
    width: 100,
    resizable: true,
  },
  {
    title: '金额',
    align: 'center',
    dataIndex: 'totalAmt',
    width: 100,
    resizable: true,
    customRender: ({ text }) => FormatNumToThousands(text)
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    width: 160,
    resizable: true,
  },
  {
    title: '批号',
    align: 'center',
    dataIndex: 'batchNo',
    width: 100,
    resizable: true,
  },
  {
    title: '生产日期',
    align: 'center',
    dataIndex: 'productDate',
    width: 140,
    resizable: true,
  },
  {
    title: '有效日期',
    align: 'center',
    dataIndex: 'term',
    width: 140,
    resizable: true,
  },
  {
    title: '灭菌日期',
    align: 'center',
    dataIndex: 'sterilizationDate',
    width: 100,
    resizable: true,
  },
  {
    title: '耗材条码',
    align: 'center',
    dataIndex: 'barCode',
    width: 220,
    resizable: true,
  },
  {
    title: 'UDI',
    align: 'center',
    dataIndex: 'udiCode',
    width: 160,
    resizable: true,
  },
  {
    title: '生产厂商',
    align: 'center',
    dataIndex: 'manufacturerName',
    width: 120,
    resizable: true,
  },
  {
    title: '厂商PN',
    align: 'center',
    dataIndex: 'udiDi',
    width: 100,
    resizable: true,
  },
  {
    title: '注册证号',
    align: 'center',
    dataIndex: 'registerNo',
    width: 160,
    resizable: true,
  },
  {
    title: '制单人',
    align: 'center',
    dataIndex: 'outUser',
    width: 100,
    resizable: true,
  },
  {
    title: '确认人',
    align: 'center',
    dataIndex: 'auditor',
    width: 100,
    resizable: true,
  },
  {
    title: '确认日期',
    align: 'center',
    dataIndex: 'auditDate',
    width: 100,
    resizable: true,
  },
  {
    title: '业务类型',
    align: 'center',
    dataIndex: 'consignmentType_dictText',
    width: 100,
    resizable: true,
  },
  {
    title: '收费项目编码',
    align: 'center',
    dataIndex: 'chargeItemsCode',
    width: 120,
    resizable: true,
  },
  {
    title: '住院号',
    align: 'center',
    dataIndex: 'liveHospitalCode',
    width: 160,
    resizable: true,
  },
  {
    title: '病历号',
    align: 'center',
    dataIndex: 'patientCode',
    width: 100,
    resizable: true,
  },
  {
    title: '病人姓名',
    align: 'center',
    dataIndex: 'patientName',
    width: 100,
    resizable: true,
  },
  {
    title: '医生姓名',
    align: 'center',
    dataIndex: 'doctorName',
    width: 100,
    resizable: true,
  },
  {
    title: '装备协会分类',
    align: 'center',
    dataIndex: 'eacName',
    width: 120,
    resizable: true,
  },
  {
    title: '结算单号',
    align: 'center',
    dataIndex: 'settlementBillsCode',
    width: 100,
    resizable: true,
  },
  {
    title: '医保编码',
    align: 'center',
    dataIndex: 'medicalInsuranceCode',
    width: 180,
    resizable: true,
  },
];
