import { BasicColumn,FormSchema } from '/@/components/Table';
import { getDepartListLeaf, getspdStoragelist } from '/@/api/common/api';
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
import dayjs, { Dayjs } from 'dayjs';
import { FormatNumToThousands } from '/@/utils/index'
const ranges = {
    今天: [dayjs().startOf("day"), dayjs()],
    明天: [
        dayjs().startOf("day"),
        dayjs().startOf("day").subtract(-1, "days"),
    ],
}
//列表数据
export const columns: BasicColumn[] = [
    {
        title: '申领单号',
        align: 'center',
        dataIndex: 'operationApplyOrderNo',
        width: 180,
        fixed: 'left',
        resizable: true,
        sorter: true,
    },
    {
        title: '手术编号',
        align: 'center',
        dataIndex: 'applyPlaneCode',
        width: 150,
        resizable: true,
        sorter: true,
    },
    {
        title: '手术日期',
        align: 'center',
        dataIndex: 'operationTime',
        width: 160,
        resizable: true,
        sorter: true,
    },
    {
        title: '手术科室',
        align: 'center',
        dataIndex: 'departName',
        width: 200,
        resizable: true,
    },
    {
        title: '申领科室',
        align: 'center',
        dataIndex: 'requestDepartName',
        width: 200,
        resizable: true,
    },
    {
        title: '术间—台次',
        align: 'center',
        dataIndex: 'operationNum',
        width: 100,
        resizable: true,
    },
    {
        title: '患者',
        align: 'center',
        dataIndex: 'patientName',
        width: 80,
        resizable: true,
    },
    {
        title: '病历号',
        align: 'center',
        dataIndex: 'liveHospitalCode',
        width: 100,
        resizable: true,
        sorter: true,
    },
    {
        title: '性别',
        align: 'center',
        dataIndex: 'patientGender_dictText',
        width: 60,
        resizable: true,
    },
    {
        title: '年龄',
        align: 'center',
        dataIndex: 'patientAge',
        width: 60,
        resizable: true,
        sorter: true,
    },
    {
        title: '手术名称',
        align: 'center',
        dataIndex: 'operationName',
        width: 180,
        resizable: true,
    },
    {
        title: '申领数量',
        align: 'center',
        dataIndex: 'applyNum',
        width: 120,
        resizable: true,
        sorter: true,
    },
    {
        title: '总金额(元)',
        align: 'center',
        dataIndex: 'totalAmount',
        width: 120,
        resizable: true,
        sorter: true,
        customRender: ({ text }) => {
            return FormatNumToThousands(text);
          },
    },
    {
        title: '发放状态',
        align: 'center',
        dataIndex: 'sendStatus_dictText',
        width: 100,
        resizable: true,
        sorter: true,
    },
    {
        title: '申请医生',
        align: 'center',
        dataIndex: 'applyUserName',
        width: 100,
        resizable: true,
    },
    {
        title: '主刀医生',
        align: 'center',
        dataIndex: 'doctorName',
        width: 100,
        resizable: true,
    },
    {
        title: '响应库房',
        align: 'center',
        dataIndex: 'storageName',
        width: 140,
        resizable: true,
    },

];
//查询数据
export const searchFormSchema: FormSchema[] = [
    {
        label: '手术日期',
        field: 'operationTime',
        component: 'RangePicker',
        componentProps: { valueType: 'Date', ranges: ranges }
    },
    {
        label: '申领单号',
        field: 'operationApplyOrderNo',
        component: 'JInput',
    },
    {
        label: '手术编号',
        field: 'applyPlaneCode',
        component: 'JInput',
    },
    {
        label: '术间-台次',
        field: 'operationNum',
        component: 'Input',
    },

    {
        label: '发放状态',
        field: 'sendStatus',
        component: 'JDictSelectTag',
        componentProps: {
            dictCode: 'send_status',
        },
    },
    {
        label: '申请医生',
        field: 'applyUserName',
        component: 'JInput',
    },
    {
        label: '主刀医生',
        field: 'doctorName',
        component: 'Input',
    },
    {
        label: '响应库房',
        field: 'storageId',
        component: 'ApiSelect',
        componentProps: {
            api: () => getspdStoragelist({ column: 'storageType', order: 'asc' }),
            params: {
            },
            showSearch: true,
            optionFilterProp: 'storageNameAbbr',
            labelField: 'storageName',
            valueField: 'id',
            allowClear: true
        },
    },
    {
        label: '手术科室',
        field: 'departId',
        component: 'ApiSelect',
        componentProps: {
            api: getDepartListLeaf,
            params: {},
            showSearch: true,
            optionFilterProp: 'departNameDepartNameAbbr',
            labelField: 'departName',
            valueField: 'id',
            allowClear: true,
        },
    },
    {
        label: '申领科室',
        field: 'requestDepartId',
        component: 'ApiSelect',
        componentProps: {
            api: getDepartListLeaf,
            params: {},
            showSearch: true,
            optionFilterProp: 'departNameDepartNameAbbr',
            labelField: 'departName',
            valueField: 'id',
            allowClear: true,
        },
    },
    {
        label: '患者姓名',
        field: 'patientName',
        component: 'JInput',
    },
    {
        label: '病历号',
        field: 'liveHospitalCode',
        component: 'JInput',
    }
];