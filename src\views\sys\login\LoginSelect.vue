<template>
  <BasicModal
    v-bind="config"
    @register="registerModal"
    :keyboard="false"
    :title="currTitle"
    wrapClassName="loginSelectModal"
    v-model:visible="visible"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      v-bind="layout"
      :colon="false"
      class="loginSelectForm"
    >
      <!--多院区选择-->
      <a-form-item
        v-if="isMultiTenant"
        name="tenantId"
        :validate-status="validate_status"
      >
        <!--label内容-->
        <template #label>
          <a-tooltip placement="topLeft">
            <template #title>
              <span>您隶属于多院区，请选择登录院区</span>
            </template>
            <a-avatar style="background-color: #87d068" :size="30"> 院区 </a-avatar>
          </a-tooltip>
        </template>
        <template #extra v-if="validate_status == 'error'">
          <span style="color: #ed6f6f">请选择登录院区</span>
        </template>
        <!--院区下拉内容-->
        <a-select
          v-model:value="formState.tenantId"
          @change="handleTenantChange"
          allowClear
          placeholder="请选择登录院区"
          :class="{ 'valid-error': validate_status == 'error' }"
        >
          <template v-for="tenant in tenantList" :key="tenant.id">
            <a-select-option :value="tenant.id">{{ tenant.name }}</a-select-option>
          </template>
        </a-select>
      </a-form-item>
      <!--多部门选择-->
      <a-form-item
        v-if="isMultiDepart"
        :validate-status="validate_status1"
        :colon="false"
      >
        <!--label内容-->
        <template #label>
          <a-tooltip placement="topLeft">
            <template #title>
              <span>您隶属于多部门，请选择登录部门</span>
            </template>
            <a-avatar style="background-color: rgb(104, 208, 203)" :size="30">
              部门
            </a-avatar>
          </a-tooltip>
        </template>
        <template #extra v-if="validate_status1 == 'error'">
          <span style="color: #ed6f6f">请选择登录部门</span>
        </template>
        <a-select
          v-model:value="formState.orgCode"
          @select="handleDepartChange"
          @change="handleChange"
          allowClear
          placeholder="请选择登录部门"
          :class="{ 'valid-error': validate_status1 == 'error' }"
        >
          <template v-for="depart in departList" :key="depart.orgCode">
            <a-select-option :value="depart.orgCode">{{
              depart.departName
            }}</a-select-option>
          </template>
        </a-select>
      </a-form-item>
      <!-- 仓库选择 -->
      <a-form-item v-if="isMultiStorage">
        <template #label>
          <a-tooltip placement="topLeft">
            <template #title>
              <span>请选择登录仓库</span>
            </template>
            <a-avatar style="background-color: rgb(24, 144, 255)" :size="30">
              仓库
            </a-avatar>
          </a-tooltip>
        </template>
        <!-- <template #extra v-if="validate_status1 == 'error'">
          <span style="color: #ed6f6f">请选择登录仓库</span>
        </template> -->
        <a-select v-model:value="formState.id" allowClear placeholder="请选择登录仓库">
          <template v-for="item in storeList" :key="item.id">
            <a-select-option :value="item.id">{{ item.storageName }}</a-select-option>
          </template>
        </a-select>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="handleSubmit" type="primary">确认</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, unref, reactive, UnwrapRef } from "vue";
import type { Ref } from "vue";
import { useRouter } from "vue-router";
import { Avatar } from "ant-design-vue";
import { BasicModal, useModalInner } from "/@/components/Modal";
import { useMessage } from "/@/hooks/web/useMessage";
import { useUserStore } from "/@/store/modules/user";
import { defHttp } from "/@/utils/http/axios";
import { queryMyDeptListByTenantId } from "./index.api";
import { storeToRefs } from "pinia";


interface FormState {
  orgCode: string | undefined;
  tenantId: number | null;
  id: string | undefined | number;
}
export default defineComponent({
  name: "loginSelect",
  components: {
    Avatar,
    BasicModal,
  },
  emits: ["success", "register"],
  setup(props, { emit }) {
    const route = useRouter();
    const userStore = useUserStore();
    const { tenantid, hospitalZoneInfo } = storeToRefs(userStore);
    const { notification } = useMessage();
    //院区配置
    const isMultiTenant = ref(false);
    const tenantList = ref([]);
    const validate_status = ref("");
    //部门配置
    const isMultiDepart = ref(false);
    const isMultiStorage = ref(false);
    const departList = ref([]);
    const validate_status1 = ref("");
    //弹窗显隐
    const visible = ref(false);
    //登录用户
    const username = ref("");
    //表单
    const formRef = ref();
    //选择的院区部门信息
    const formState: UnwrapRef<FormState> = reactive({
      orgCode: undefined,
      tenantId: null,
      id: undefined,
    });

    const config = {
      maskClosable: false,
      closable: false,
      canFullscreen: false,
      width: "500px",
      minHeight: 20,
      maxHeight: 20,
    };
    //弹窗操作
    const [registerModal, { closeModal }] = useModalInner();
    //当前标题
    const currTitle = computed(() => {
      if (unref(isMultiDepart) && unref(isMultiTenant)) {
        return "请选择院区和部门";
      } else if (unref(isMultiDepart) && !unref(isMultiTenant)) {
        return "请选择部门";
      } else if (!unref(isMultiDepart) && unref(isMultiTenant)) {
        return "请选择院区";
      } else if (!unref(isMultiDepart) && !unref(isMultiTenant)) {
        return "请选择仓库";
      }
    });

    const rules = ref({
      tenantId: [
        {
          required: unref(isMultiTenant),
          type: "string",
          message: "请选择院区",
          trigger: "change",
        },
      ],
      orgCode: [
        { required: unref(isMultiDepart), message: "请选择部门", trigger: "change" },
      ],
    });

    const layout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    /**
     * 处理部门情况
     */
    async function bizDepart(loginResult) {
      // console.log(loginResult, 'loginResult');

      let multi_depart = loginResult.multi_depart;
      let departs = loginResult.departs;
      // console.log(departs);
      let orgCode;
      if (departs[0]) {
        orgCode = departs[0].orgCode;
      }

      //0:无部门 1:一个部门 2:多个部门
      if (multi_depart == 0) {
        notification.warn({
          message: "提示",
          description: `您尚未归属部门,请确认账号信息`,
          duration: 3,
        });
        isMultiDepart.value = false;
      } else if (multi_depart == 2) {
        isMultiDepart.value = true;
        isMultiStorage.value = true;
        departList.value = loginResult.departs;
      } else {
        isMultiDepart.value = true;
        await getStoreList(orgCode);
        // console.log(storeList.value,'storeList.value');
        isMultiStorage.value = true;
        departList.value = loginResult.departs;
        // if (storeList.value.length > 1) {
        //   isMultiStorage.value = true;
        // } else {
        //   isMultiStorage.value = false;
        // }
      }
    }

    /**
     * 处理院区情况
     */
    function bizTenantList(loginResult) {
      // console.log(loginResult, 'loginResult');

      let tenantArr = loginResult.tenantList;
      if (Array.isArray(tenantArr)) {
        if (tenantArr.length === 0) {
          isMultiTenant.value = false;
          userStore.setTenant(formState.tenantId);
        } else if (tenantArr.length === 1) {
          // console.log(tenantArr[0]);

          formState.tenantId = tenantArr[0].id;
          isMultiTenant.value = true;
          tenantList.value = tenantArr;
          userStore.setTenant(formState.tenantId);
        } else {
          isMultiTenant.value = true;
          tenantList.value = tenantArr;
        }
      }
    }

    /**
     * 确认选中的院区和部门信息
     */
    async function handleSubmit() {
      if (unref(isMultiTenant) && !formState.tenantId) {
        validate_status.value = "error";
        return false;
      }
      if (unref(isMultiDepart) && !formState.orgCode) {
        validate_status1.value = "error";
        return false;
      }
      console.log(route);
      formRef.value
        .validate()
        .then(() => {
          departResolve()
            .then(() => {
              // console.log(tenantList.value, '  tenantList.value');
              let obj = tenantList.value.filter(
                (item) => item.id == formState.tenantId
              )[0];
              hospitalZoneInfo.value.comp = obj;
              console.log(hospitalZoneInfo.value, "hospitalZoneInfo.value");
              localStorage.setItem("tenantId", hospitalZoneInfo.value?.comp.id);
              emit("success");
              route.go(0)
            })
            .catch((e) => {
              console.log("登录选择出现问题", e);
            })
            .finally(() => {
              close();
            });
        })
        .catch((err) => {
          console.log("表单校验未通过error", err);
        });
    }
    /**
     *切换选择部门
     */
    function departResolve() {
      return new Promise((resolve, reject) => {
        if (!unref(isMultiDepart) && !unref(isMultiStorage)) {
          resolve();
        } else {
          let params = {
            orgCode: formState.orgCode,
            storageId: formState.id,
            username: unref(username),
          };
          defHttp.put({ url: "/sys/selectDepart", params }).then((res) => {});
          defHttp.put({ url: "/sys/selectDepartAndStorage", params }).then((res) => {
            if (res.userInfo) {
              userStore.setUserInfo(res.userInfo);
              resolve();
            } else {
              requestFailed(res);
              userStore.logout();
              reject();
            }
          });
        }
      });
    }

    /**
     * 请求失败处理
     */
    function requestFailed(err) {
      notification.error({
        message: "登录失败",
        description:
          ((err.response || {}).data || {}).message ||
          err.message ||
          "请求出现错误，请稍后再试",
        duration: 4,
      });
    }

    /**
     * 关闭model
     */
    function close() {
      closeModal();
      reset();
    }
    /**
     * 弹窗打开前处理
     */
    async function show(loginResult) {
      if (loginResult) {
        username.value = userStore.username;
        await reset();
        await bizDepart(loginResult);
        await bizTenantList(loginResult);
        if (!unref(isMultiDepart) && !unref(isMultiTenant) && !unref(isMultiStorage)) {
          emit("success", userStore.getUserInfo);
        } else {
          visible.value = true;
        }
      }
      //登录弹窗完成后，将登录的标识设置成false
      loginResult.isLogin = false;
      userStore.setLoginInfo(loginResult);
    }

    /**
     *重置数据
     */
    function reset() {
      tenantList.value = [];
      validate_status.value = "";

      departList.value = [];
      validate_status1.value = "";
    }

    function handleTenantChange(e) {
      userStore.setTenant(e);
      queryMyDeptListByTenantId({}).then((res) => {
        // console.log(res,'res');
        storeList.value = [];
        departList.value = res;
        // 如果只有一个部门 直接展示 仓库根据部门id获取 所以 如果只有一个部门 需要调用获取仓库函数
        if (res.length === 1) {
          formState.orgCode = res[0].orgCode;
          handleDepartChange(formState.orgCode);
        } else {
          formState.orgCode = undefined;
          formState.id = undefined;
          storeList.value = [];
        }
      });
      validate_status.value = "";
    }
    // 清除部门
    const handleChange = (e) => {
      if (!e) {
        storeList.value = [];
        formState.id = undefined;
      }
    };
    async function handleDepartChange(e) {
      formState.id = undefined;
      await getStoreList(e);
      formState.id = storeList.value.length > 0 ? storeList.value[0].id : "";
      validate_status1.value = "";
    }
    // 获取仓库列表
    type StoreList = {
      storageName: string;
      id: number;
    };
    const storeList: Ref<StoreList[]> = ref([]); // 仓库列表
    const getStoreList = async (e) => {
      const res = await defHttp.get({
        url: "/spd/spdStorage/queryStorageByDepartCode",
        params: { departCode: e },
      });
      storeList.value = res.map((item) => {
        return {
          storageName: item.storageName,
          id: item.id,
        };
      });
      // formState.id = storeList.value.length>0?storeList.value[0].id:''
    };
    return {
      registerModal,
      visible,
      tenantList,
      isMultiTenant,
      validate_status,
      isMultiDepart,
      isMultiStorage,
      departList,
      storeList,
      validate_status1,
      formState,
      rules,
      layout,
      formRef,
      currTitle,
      config,
      handleTenantChange,
      handleDepartChange,
      handleChange,
      show,
      handleSubmit,
      getStoreList,
    };
  },
});
</script>

<style lang="less" scoped>
.loginSelectForm {
  margin-bottom: -20px;
}

.loginSelectModal {
  top: 10px;
}
</style>
