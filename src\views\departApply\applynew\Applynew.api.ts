import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/spd/spdApplyOrder/list',
  getSpdApplyOrderDetailllist = '/spd/spdApplyOrderDetail/listByApplyOrderId',
  applyOrderHisVoList = '/spd/spdApplyOrder/applyOrderHisVoList',
  addApplyOrder = '/spd/spdApplyOrder/addByDepart',
  edit = '/spd/spdApplyOrder/editByDepart',
  deleteOne = '/spd/spdApplyOrder/delete',
  auditBatch = '/spd/spdApplyOrder/auditBatch',
  queryApplyOrderPrint = '/spd/spdApplyOrder/queryApplyOrderPrint',
  firstAuditBatch = '/spd/spdApplyOrder/firstAuditBatch', // 一级审核
  detailSummary = '/spd/spdApplyOrder/detailSummary', // 一级审核
  exportForIds = '/spd/spdApplyOrder/exportForIds', // 导出
  getLastMonthCheckStatus = '/spd/inventoryCheckTask/getLastMonthCheckStatus', // 
}
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });
export const firstAuditBatch = (data) => defHttp.post({ url: Api.firstAuditBatch, data });
export const detailSummary = (data) => defHttp.post({ url: Api.detailSummary, data }); //单据汇总
//获取申领单明细
export const getSpdApplyOrderDetailllist = (params) => defHttp.get({ url: Api.getSpdApplyOrderDetailllist, params });
export const applyOrderHisVoList = (params) => defHttp.get({ url: Api.applyOrderHisVoList, params });
export const queryApplyOrderPrint = (params) => defHttp.get({ url: Api.queryApplyOrderPrint, params });
export const getLastMonthCheckStatus = (params?) => defHttp.get({ url: Api.getLastMonthCheckStatus, params });

/**
 * 添加计划
 * @param params
 */
export const addApplyOrder = (params) => {
  return defHttp.post({ url: Api.addApplyOrder, params });
};

/**
 * 编辑计划
 * @param params
 */
export const editApplyOrder = (params) => {
  return defHttp.post({ url: Api.edit, params });
};

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 单个审核，退回，撤回，提交
 * @param params
 */
export const audit = (params, handleSuccess) => {
  return defHttp.post({ url: Api.auditBatch, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量审核，退回
 * @param params
 */
export const auditBatch = (params, msg, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认'+msg,
    content: '是否'+msg+'选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.post({ url: Api.auditBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
export const exportForIds =  (params?: any) => defHttp.get({ url: Api.exportForIds, params, responseType: 'blob', }, { isReturnNativeResponse: true }); 