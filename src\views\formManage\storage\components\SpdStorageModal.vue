<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose title="仓库耗材详情" :width="1300">
    <BasicTable @register="registerTable">
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
      </div>
    </template>
    </BasicTable>
    <SpdStorageMRModal @register="registerModal1" @success="handleSuccess"></SpdStorageMRModal>
    <BarcodePrinter v-if="printBool" ref="goodsPrintRef" :list="printList" :count="1" @success="handleSuccess"></BarcodePrinter>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { columnsM } from '../SpdStorage.data';
import { listM } from '../SpdStorage.api';
import BarcodePrinter from './BarcodePrinter.vue'
import SpdStorageMRModal from "./SpdStorageMRModal.vue";
import { MyPrintListt } from './type';
import { handleSummaryNew } from '/@/utils/index'
const getFootTotal = (currentPageData)=> handleSummaryNew(currentPageData,'totalAmt','currentNum')
// Emits声明
//const emit = defineEmits(['register','success']);

const isUpdate = ref(true);
//注册Model
const [registerModal1, { openModal }] = useModal();

//表单赋值
const [registerModal, { setModalProps }] = useModalInner(async (data) => {
  goods=data.record
  setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  setProps({ searchInfo: { inventoryId: data?.record.id } });
});
//表单配置
const [registerTable, { setProps,reload }] = useTable({
  // title: '仓库耗材详情',
  api: listM,
  rowKey: 'id',
  bordered: true,
  columns: columnsM,
  pagination: true,
  showIndexColumn: true,
  scroll: { y: 300 },
  actionColumn: {
    width: 160,
    title: '操作',
    fixed: 'right',
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
});


/**
 * 详情
*/
function handleDetail(record: Recordable) {
  printBool.value = false
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
const printBool = ref(false)
const printList = ref<MyPrintListt[]>([])
const goodsPrintRef = ref()
let goods:any=ref()
const handlePrint = (record: Recordable) => {
  printBool.value = true
  let Labels = [record].map(item => {
    let term = item.term.split(' ')[0]
    return {
      "ItemName": goods.goodsName,
      "LotID": item.batchNo,
      "ExpireDate": term,
      "RFIDValue": goods.batchCode,
      "ItemStandard": goods.goodsSpecs,
      "ItemManufacturer": goods.manufacturerName,
      "ItemModel": goods.goodsSpecsDetail,
      "BarCodeValue": item.batchCode
    }
  })
  const params = {
    Labels,
  }
  printList.value = params.Labels
  setTimeout(function () {
    goodsPrintRef.value.print();
  }, 100);
}
/**
   * 操作栏
   */
function getTableAction(record) {
  return [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '打印定数包码',
      onClick: handlePrint.bind(null, record),
      ifShow: (_action) => {
        return record.quantitativePackageFlag == 1
      }
    }
  ]
}
/**
 * 成功回调
 */
function handleSuccess() {
    reload();
}



</script>

<style lang="less" scoped>
/** 时间和数字输入框样式 */
:deep(.ant-input-number) {
  width: 100%
}

:deep(.ant-calendar-picker) {
  width: 100%
}
</style>
