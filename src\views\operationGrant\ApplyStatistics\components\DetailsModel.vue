<template>
  <BasicModal @register="registerModal"  :height="800" destroyOnClose>
    <BasicTable @register="registerTable">
      <template #expandedRowRender="{ record }">
        <a-table :columns="innerColumns" :data-source="record.recordList" :pagination="false"> </a-table>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicTable, useTable } from '/@/components/Table';
import { detailsColumns } from '../index.data';
import { detailList } from '../index.api';
import { ref } from 'vue';

let tableData: any = ref([]);
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  tableData.value = [];
  let res = await detailList({
    goodsCode: data.record.goodsCode,
    operationEndDate: data.record.operationEndDate,
    operationStartDate: data.record.operationStartDate,
  });

  tableData.value = res.records;
  setModalProps({
    width: 1600,
    title: '物资名称：' + data.record.goodsName + ' 物资编码：' + data.record.goodsCode,

  });
});
const [registerTable] = useTable({
  dataSource: tableData,
  pagination: true,
  columns: detailsColumns,
  scroll: { y: 500 },
  showIndexColumn: true,
  showActionColumn: false,
});
const innerColumns = [{ title: '已发放条码', dataIndex: 'barCode', key: 'barCode', align: 'center' }];






</script>
<style lang="scss" scoped></style>
